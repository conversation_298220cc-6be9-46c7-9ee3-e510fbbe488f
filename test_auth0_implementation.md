# 🧪 Checklist de Testes - Implementação Auth0

## ✅ Testes de Backend

### 1. Middleware Auth0
- [ ] **Teste sem token**: Acessar rota protegida sem token deve retornar 401
- [ ] **Teste token inválido**: Usar token malformado deve retornar 401
- [ ] **Teste token expirado**: Usar token expirado deve retornar 401
- [ ] **Teste sem role Admin**: Token válido sem role Admin deve retornar 403
- [ ] **Teste com role Admin**: Token válido com role Admin deve permitir acesso

### 2. Endpoints Auth0
```bash
# Teste de verificação (requer token válido com role Admin)
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:5555/api/auth/verify

# Teste de informações do usuário (requer token válido)
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:5555/api/auth/user

# Teste de logout
curl -X POST http://localhost:5555/api/auth/logout
```

### 3. R<PERSON>s Protegidas
Testar algumas rotas principais:
```bash
# Servidores
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:5555/api/server/configs

# Agendamentos
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:5555/api/schedules

# Apps Qlik
curl -H "Authorization: Bearer YOUR_TOKEN" "http://localhost:5555/api/qlik/apps?serverId=SERVER_ID"
```

## ✅ Testes de Frontend

### 1. Fluxo de Autenticação
- [ ] **Página inicial**: Usuário não logado deve ser redirecionado para login
- [ ] **Login Auth0**: Clicar em "Entrar com Auth0" deve abrir popup/redirect Auth0
- [ ] **Callback**: Após login bem-sucedido, deve retornar à aplicação
- [ ] **Verificação de role**: Usuário sem role Admin deve ver tela de "Acesso Negado"
- [ ] **Acesso permitido**: Usuário com role Admin deve acessar dashboard

### 2. Interface do Usuário
- [ ] **Sidebar**: Nome e email do usuário devem aparecer na sidebar
- [ ] **Roles**: Role "Admin" deve aparecer nas informações do usuário
- [ ] **Logout**: Botão de logout deve funcionar corretamente
- [ ] **Redirecionamento**: Após logout, deve voltar para página de login

### 3. Chamadas de API
- [ ] **Token automático**: Chamadas via apiService devem incluir token automaticamente
- [ ] **Renovação**: Token deve ser renovado automaticamente quando necessário
- [ ] **Erro 401**: Erro de token deve ser tratado adequadamente
- [ ] **Funcionalidades**: Todas as funcionalidades existentes devem funcionar

## 🔧 Como Executar os Testes

### 1. Preparação do Ambiente

#### Backend
```bash
cd backend
npm install
# Configurar .env com variáveis Auth0
npm start
```

#### Frontend
```bash
cd frontend_AUTOMATION-MANAGER/frontend
npm install
# Configurar .env.local com variáveis Auth0
npm run dev
```

### 2. Configuração Auth0

#### Criar Usuário de Teste
1. Acessar Auth0 Dashboard
2. Ir em User Management > Users
3. Criar usuário com email/senha
4. Atribuir role "Admin" ao usuário

#### Obter Token para Testes de API
```javascript
// No console do navegador após login
const { getAccessTokenSilently } = useAuth0();
const token = await getAccessTokenSilently();
console.log('Token:', token);
```

### 3. Testes Manuais

#### Cenário 1: Usuário sem Role Admin
1. Criar usuário no Auth0 sem role Admin
2. Tentar fazer login
3. Verificar se aparece tela "Acesso Negado"
4. Verificar logs do backend

#### Cenário 2: Usuário com Role Admin
1. Atribuir role Admin ao usuário
2. Fazer login
3. Verificar se acessa dashboard
4. Testar funcionalidades principais

#### Cenário 3: Token Expirado
1. Deixar aplicação aberta por tempo prolongado
2. Verificar se token é renovado automaticamente
3. Testar chamadas de API após renovação

## 🐛 Problemas Esperados e Soluções

### Problema: "Audience inválida"
**Solução**: Verificar se AUTH0_AUDIENCE no backend e NEXT_PUBLIC_AUTH0_AUDIENCE no frontend são iguais

### Problema: "Role não encontrada"
**Solução**: 
1. Verificar se role "Admin" existe no Auth0
2. Verificar se usuário tem a role atribuída
3. Verificar configuração de custom claims

### Problema: CORS Error
**Solução**: 
1. Verificar Allowed Origins no Auth0
2. Verificar se NEXT_PUBLIC_FRONTEND_URL está correto

### Problema: "Token não incluído nas requisições"
**Solução**: 
1. Verificar se apiService.setTokenFunction foi chamado
2. Verificar se useAuth está funcionando corretamente

## 📊 Métricas de Sucesso

### Funcionalidade ✅
- [ ] Login funciona com Auth0
- [ ] Apenas usuários Admin acessam a aplicação
- [ ] Todas as funcionalidades existentes funcionam
- [ ] Logout funciona corretamente

### Performance ✅
- [ ] Tempo de login < 3 segundos
- [ ] Renovação de token transparente
- [ ] Chamadas de API não são mais lentas

### Segurança ✅
- [ ] Tokens são verificados no backend
- [ ] Roles são verificadas em cada requisição
- [ ] Usuários sem permissão são bloqueados
- [ ] Tokens expirados são rejeitados

## 📝 Relatório de Testes

### Data: ___________
### Testador: ___________

#### Resultados:
- [ ] ✅ Todos os testes passaram
- [ ] ⚠️ Alguns testes falharam (especificar abaixo)
- [ ] ❌ Implementação precisa de correções

#### Observações:
```
[Espaço para anotações sobre problemas encontrados, 
performance observada, sugestões de melhoria, etc.]
```

#### Próximos Passos:
- [ ] Corrigir problemas identificados
- [ ] Implementar melhorias sugeridas
- [ ] Documentar configurações finais
- [ ] Treinar usuários finais

---

**Importante**: Todos os testes devem passar antes de considerar a migração completa para produção.
