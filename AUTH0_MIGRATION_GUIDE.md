# 🔐 Guia de Migração para Auth0

Este documento descreve as mudanças implementadas na migração do sistema de autenticação baseado em MongoDB para Auth0.

## 📋 Resumo das Mudanças

### ✅ Implementado

#### Backend (Node.js/Express)
- ✅ **Middleware Auth0**: Criado `backend/middleware/auth0.js` com verificação JWT e role Admin
- ✅ **Endpoints Auth0**: Novo arquivo `backend/endpoints/auth0.js` com rotas de verificação
- ✅ **Proteção de Rotas**: Todas as rotas protegidas com middleware `requireAuth`
- ✅ **Variáveis de Ambiente**: Configuração Auth0 em `.env.example`
- ✅ **Compatibilidade**: Sistema antigo mantido para transição gradual

#### Frontend (Next.js/React)
- ✅ **Auth0 Provider**: Configurado `@auth0/auth0-react` em `src/contexts/AuthProvider.tsx`
- ✅ **Hook Personalizado**: `src/hooks/useAuth.ts` com verificação de role Admin
- ✅ **Proteção de Rotas**: Componente `src/components/ProtectedRoute.tsx`
- ✅ **Serviço de API**: `src/services/api.ts` com tokens Auth0 automáticos
- ✅ **Interface de Login**: Nova página de login Auth0
- ✅ **Sidebar Atualizada**: Informações do usuário Auth0

## 🔧 Configuração Necessária

### 1. Variáveis de Ambiente

#### Backend (.env)
```env
# Auth0 Configuration
AUTH0_DOMAIN=dev-yz8te7p2q5nhata0.us.auth0.com
AUTH0_AUDIENCE=https://minha-api
```

#### Frontend (.env.local)
```env
# Auth0 Configuration
NEXT_PUBLIC_AUTH0_DOMAIN=dev-yz8te7p2q5nhata0.us.auth0.com
NEXT_PUBLIC_AUTH0_CLIENT_ID=EXEMPLOCLIENTEID123
NEXT_PUBLIC_AUTH0_AUDIENCE=https://minha-api

# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:5555/api
NEXT_PUBLIC_FRONTEND_URL=http://localhost:3000
```

### 2. Configuração Auth0

#### Aplicação Auth0
1. Criar aplicação Single Page Application no Auth0
2. Configurar Allowed Callback URLs: `http://localhost:3000`
3. Configurar Allowed Logout URLs: `http://localhost:3000`
4. Configurar Allowed Web Origins: `http://localhost:3000`

#### API Auth0
1. Criar API no Auth0 com identifier: `https://minha-api`
2. Habilitar RBAC (Role-Based Access Control)
3. Adicionar role "Admin" no Auth0 Dashboard

#### Usuários
1. Criar usuários no Auth0 Dashboard
2. Atribuir role "Admin" aos usuários autorizados
3. Configurar custom claims para roles (opcional)

## 🚀 Como Usar

### Autenticação
```typescript
import { useAuth } from '@/hooks/useAuth';

function MyComponent() {
  const { user, isAuthenticated, isAdmin, login, logout } = useAuth();
  
  if (!isAuthenticated) {
    return <button onClick={login}>Login</button>;
  }
  
  if (!isAdmin) {
    return <div>Acesso negado - Role Admin necessária</div>;
  }
  
  return <div>Bem-vindo, {user?.name}!</div>;
}
```

### Chamadas de API
```typescript
import { apiService } from '@/services/api';

// O token é automaticamente incluído
const schedules = await apiService.getSchedules();
const servers = await apiService.getServers();
```

## 🔒 Segurança

### Verificação de Role Admin
- **Backend**: Middleware `checkAdminRole` verifica role em cada requisição
- **Frontend**: Hook `useAuth` verifica role antes de renderizar conteúdo
- **Dupla Proteção**: Verificação tanto no cliente quanto no servidor

### Tokens JWT
- **Verificação**: Tokens verificados usando JWKS do Auth0
- **Expiração**: Tokens renovados automaticamente
- **Escopo**: Audience específica para a API

## 📁 Estrutura de Arquivos

### Backend
```
backend/
├── middleware/
│   └── auth0.js              # Middleware de autenticação Auth0
├── endpoints/
│   ├── auth0.js              # Endpoints Auth0
│   ├── login.js              # [DEPRECIADO] Sistema antigo
│   └── createUser.js         # [DEPRECIADO] Sistema antigo
└── .env.example              # Variáveis de ambiente
```

### Frontend
```
frontend/src/
├── contexts/
│   └── AuthProvider.tsx      # Provider Auth0
├── hooks/
│   └── useAuth.ts            # Hook personalizado
├── components/
│   └── ProtectedRoute.tsx    # Proteção de rotas
├── services/
│   └── api.ts                # Serviço de API
└── app/
    └── login/
        └── page.tsx          # Nova página de login
```

## 🔄 Migração Gradual

### Fase 1: Implementação (Atual)
- ✅ Sistema Auth0 implementado
- ✅ Sistema antigo mantido para compatibilidade
- ✅ Rotas protegidas com Auth0

### Fase 2: Testes
- [ ] Testar fluxo completo de autenticação
- [ ] Validar verificação de roles
- [ ] Testar todas as funcionalidades existentes

### Fase 3: Limpeza
- [ ] Remover sistema antigo de autenticação
- [ ] Remover modelos User e Counter do MongoDB
- [ ] Limpar código depreciado

## 🐛 Troubleshooting

### Problemas Comuns

#### "Token inválido ou expirado"
- Verificar configuração AUTH0_DOMAIN e AUTH0_AUDIENCE
- Verificar se o usuário tem role Admin
- Tentar fazer logout e login novamente

#### "Acesso negado"
- Verificar se o usuário tem role "Admin" no Auth0
- Verificar configuração de custom claims
- Verificar logs do backend para detalhes

#### Erro de CORS
- Verificar configuração de Allowed Origins no Auth0
- Verificar se NEXT_PUBLIC_FRONTEND_URL está correto

### Logs Úteis
```bash
# Backend
tail -f backend/logs/log-$(date +%d-%m-%Y).log

# Frontend (Console do navegador)
# Verificar erros de autenticação e chamadas de API
```

## 📞 Suporte

Para dúvidas sobre a implementação Auth0:
1. Verificar logs do backend e frontend
2. Consultar documentação oficial do Auth0
3. Verificar configurações no Auth0 Dashboard

---

**Nota**: Este sistema garante que apenas usuários com role "Admin" no Auth0 possam acessar a aplicação, mantendo todas as funcionalidades existentes intactas.
