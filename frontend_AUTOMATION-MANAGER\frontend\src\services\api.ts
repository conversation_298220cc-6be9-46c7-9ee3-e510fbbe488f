import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

/**
 * Serviço de API que funciona com Auth0
 * Recebe uma função para obter tokens quando necessário
 */
class ApiService {
  private api: AxiosInstance;
  private getTokenFn: (() => Promise<string | null>) | null = null;

  constructor() {
    this.api = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  /**
   * Configura a função para obter tokens
   */
  setTokenProvider(getTokenFn: () => Promise<string | null>) {
    this.getTokenFn = getTokenFn;
  }

  /**
   * Obtém o token usando a função configurada
   */
  private async getToken(): Promise<string | null> {
    if (!this.getTokenFn) {
      console.warn('⚠️ [API] Token provider não configurado');
      return null;
    }
    return await this.getTokenFn();
  }

  /**
   * Configura interceptors para tratar erros
   */
  private setupInterceptors() {
    // Response interceptor - trata erros de autenticação
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          console.log('🔄 [API] Token expirado (401) - limpando dados e redirecionando...');
          
          // Limpar dados locais
          if (typeof window !== 'undefined') {
            localStorage.removeItem('user_data');
            localStorage.removeItem('@@auth0spajs@@::' + process.env.NEXT_PUBLIC_AUTH0_CLIENT_ID);
            
            // Redirecionar para login
            window.location.href = '/login';
          }
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Método auxiliar para fazer requisições com autenticação
   */
  private async makeAuthenticatedRequest<T = any>(
    config: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    const token = await this.getToken();
    
    console.log('🔍 [API] Fazendo requisição autenticada:', { 
      url: config.url, 
      method: config.method,
      token: token ? 'Encontrado' : 'Não encontrado'
    });
    
    if (!token) {
      throw new Error('Token de autenticação não encontrado - verifique se está logado');
    }
    
    const requestConfig = {
      ...config,
      headers: {
        ...config.headers,
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };
    
    console.log('🔍 [API] Config da requisição:', {
      ...requestConfig,
      headers: {
        ...requestConfig.headers,
        'Authorization': `Bearer ${token.substring(0, 20)}...`
      }
    });

    try {
      const response = await this.api.request<T>(requestConfig);
      console.log('✅ [API] Resposta recebida:', {
        status: response.status,
        statusText: response.statusText,
        dataType: typeof response.data,
        dataLength: Array.isArray(response.data) ? response.data.length : 'N/A'
      });
      return response;
    } catch (error) {
      console.error('❌ [API] Erro na requisição:', error);
      throw error;
    }
  }

  /**
   * Verificar autenticação do usuário
   */
  async verifyAuth(): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'GET',
      url: '/api/auth/verify',
    });
  }

  /**
   * Obter lista de agendamentos
   */
  async getSchedules(): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'GET',
      url: '/api/schedules',
    });
  }

  /**
   * Obter lista de empresas
   */
  async getCompanies(): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'GET',
      url: '/api/empresas',
    });
  }

  /**
   * Atualizar empresa
   */
  async updateCompany(id: string, data: any): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'PUT',
      url: `/api/empresas/${id}`,
      data,
    });
  }

  /**
   * Deletar empresa
   */
  async deleteCompany(id: string): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'DELETE',
      url: `/api/empresas/${id}`,
    });
  }

  /**
   * Obter lista de servidores
   */
  async getServers(): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'GET',
      url: '/api/server/configs',
    });
  }

  /**
   * Obter lista de configurações SMTP
   */
  async getSMTPConfigs(): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'GET',
      url: '/api/smtp/config/list',
    });
  }

  /**
   * Obter lista de destinatários
   */
  async getRecipients(): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'GET',
      url: '/api/destinatarios/list',
    });
  }

  /**
   * Obter lista de servidores NPrinting
   */
  async getNPrintingServers(): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'GET',
      url: '/api/nprinting/server/list',
    });
  }

  /**
   * Obter lista de servidores Qlik Cloud
   */
  async getQlikCloudServers(): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'GET',
      url: '/api/qlikcloud/server/configs',
    });
  }

  /**
   * Executar agendamento manualmente
   */
  async executeSchedule(id: string): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'POST',
      url: `/api/schedules/${id}/execute`,
    });
  }

  /**
   * Deletar agendamento
   */
  async deleteSchedule(id: string): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'DELETE',
      url: `/api/schedules/${id}`,
    });
  }

  /**
   * Atualizar agendamento
   */
  async updateSchedule(id: string, data: any): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'PUT',
      url: `/api/schedules/${id}`,
      data,
    });
  }

  /**
   * Criar novo agendamento
   */
  async createSchedule(data: any): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'POST',
      url: '/api/schedules',
      data,
    });
  }

  /**
   * Obter histórico de execução de um agendamento
   */
  async getScheduleHistory(id: string): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'GET',
      url: `/api/schedules/${id}/history`,
    });
  }

  /**
   * Obter logs em tempo real de um agendamento
   */
  async getScheduleLogs(id: string): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'GET',
      url: `/api/schedules/${id}/logs`,
    });
  }

  /**
   * Obter status de execução de um agendamento
   */
  async getScheduleExecutionStatus(id: string): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'GET',
      url: `/api/schedules/${id}/executing`,
    });
  }

  /**
   * Obter tasks NPrinting de um servidor
   */
  async getNPrintingTasks(serverId: string): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'GET',
      url: `/api/nprinting/server/${serverId}/tasks`,
    });
  }

  /**
   * Obter um servidor NPrinting específico
   */
  async getNPrintingServer(id: string): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'GET',
      url: `/api/nprinting/server/${id}`,
    });
  }

  /**
   * Obter aplicativos Qlik de um servidor
   */
  async getQlikApps(serverId: string): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'GET',
      url: `/api/qlik/server/${serverId}/apps`,
    });
  }

  /**
   * Obter aplicativos Qlik Cloud de um servidor
   */
  async getQlikCloudApps(serverId: string): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'GET',
      url: `/api/qlikcloud/server/${serverId}/apps`,
    });
  }

  /**
   * Obter tasks Qlik de um servidor
   */
  async getQlikTasks(serverId: string): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'GET',
      url: `/api/qlik/server/${serverId}/tasks`,
    });
  }

  /**
   * Obter uma configuração SMTP específica
   */
  async getSMTPConfig(id: string): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'GET',
      url: `/api/smtp/config/${id}`,
    });
  }

  /**
   * Obter destinatários por empresa
   */
  async getRecipientsByCompany(companyId: string): Promise<AxiosResponse<any>> {
    return this.makeAuthenticatedRequest({
      method: 'GET',
      url: `/api/destinatarios/list/${companyId}`,
    });
  }

  /**
   * Obter destinatários por IDs
   */
  async getRecipientsByIds(destinatarios: { value: string; label: string }[]): Promise<AxiosResponse<any>> {
    const ids = destinatarios.map(dest => dest.value);
    return this.makeAuthenticatedRequest({
      method: 'POST',
      url: '/api/destinatarios/by-ids',
      data: { ids },
    });
  }

  /**
   * Método genérico para fazer requisições
   */
  async request<T>(config: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.makeAuthenticatedRequest<T>(config);
  }
}

export const apiService = new ApiService();
