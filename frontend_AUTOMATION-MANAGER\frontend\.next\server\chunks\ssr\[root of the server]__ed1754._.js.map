{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/hooks/use-mobile.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nconst MO<PERSON>LE_BREAKPOINT = 768\n\nexport function useIsMobile() {\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\n\n  React.useEffect(() => {\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\n    const onChange = () => {\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    }\n    mql.addEventListener(\"change\", onChange)\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    return () => mql.removeEventListener(\"change\", onChange)\n  }, [])\n\n  return !!isMobile\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,sMAAM,QAAQ,CAAsB;IAEpE,sMAAM,SAAS,CAAC;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX"}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,sMAAM,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,sMAAM,UAAU,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Separator = React.forwardRef<\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\n>(\n  (\n    { className, orientation = \"horizontal\", decorative = true, ...props },\n    ref\n  ) => (\n    <SeparatorPrimitive.Root\n      ref={ref}\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"shrink-0 bg-border\",\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSeparator.displayName = SeparatorPrimitive.Root.displayName\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,0BAAY,sMAAM,UAAU,CAIhC,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,8OAAC,sKAAmB,IAAI;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG,sKAAmB,IAAI,CAAC,WAAW"}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Sheet = SheetPrimitive.Root\n\nconst SheetTrigger = SheetPrimitive.Trigger\n\nconst SheetClose = SheetPrimitive.Close\n\nconst SheetPortal = SheetPrimitive.Portal\n\nconst SheetOverlay = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\n\nconst sheetVariants = cva(\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n  {\n    variants: {\n      side: {\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\n        bottom:\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\n        right:\n          \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\n      },\n    },\n    defaultVariants: {\n      side: \"right\",\n    },\n  }\n)\n\ninterface SheetContentProps\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\n    VariantProps<typeof sheetVariants> {}\n\nconst SheetContent = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Content>,\n  SheetContentProps\n>(({ side = \"right\", className, children, ...props }, ref) => (\n  <SheetPortal>\n    <SheetOverlay />\n    <SheetPrimitive.Content\n      ref={ref}\n      className={cn(sheetVariants({ side }), className)}\n      {...props}\n    >\n      {children}\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </SheetPrimitive.Close>\n    </SheetPrimitive.Content>\n  </SheetPortal>\n))\nSheetContent.displayName = SheetPrimitive.Content.displayName\n\nconst SheetHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetHeader.displayName = \"SheetHeader\"\n\nconst SheetFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetFooter.displayName = \"SheetFooter\"\n\nconst SheetTitle = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\n    {...props}\n  />\n))\nSheetTitle.displayName = SheetPrimitive.Title.displayName\n\nconst SheetDescription = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nSheetDescription.displayName = SheetPrimitive.Description.displayName\n\nexport {\n  Sheet,\n  SheetPortal,\n  SheetOverlay,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAEA;AAGA;AAJA;AAEA;AALA;;;;;;;AASA,MAAM,QAAQ,mKAAe,IAAI;AAEjC,MAAM,eAAe,mKAAe,OAAO;AAE3C,MAAM,aAAa,mKAAe,KAAK;AAEvC,MAAM,cAAc,mKAAe,MAAM;AAEzC,MAAM,6BAAe,sMAAM,UAAU,CAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAe,OAAO;QACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAGT,aAAa,WAAW,GAAG,mKAAe,OAAO,CAAC,WAAW;AAE7D,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,oMACA;IACE,UAAU;QACR,MAAM;YACJ,KAAK;YACL,QACE;YACF,MAAM;YACN,OACE;QACJ;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAOF,MAAM,6BAAe,sMAAM,UAAU,CAGnC,CAAC,EAAE,OAAO,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,mKAAe,OAAO;gBACrB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;oBAAE;gBAAK,IAAI;gBACtC,GAAG,KAAK;;oBAER;kCACD,8OAAC,mKAAe,KAAK;wBAAC,WAAU;;0CAC9B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,aAAa,WAAW,GAAG,mKAAe,OAAO,CAAC,WAAW;AAE7D,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAe,KAAK;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG,mKAAe,KAAK,CAAC,WAAW;AAEzD,MAAM,iCAAmB,sMAAM,UAAU,CAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAe,WAAW;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,mKAAe,WAAW,CAAC,WAAW"}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,kBAAkB,oKAAiB,QAAQ;AAEjD,MAAM,UAAU,oKAAiB,IAAI;AAErC,MAAM,iBAAiB,oKAAiB,OAAO;AAE/C,MAAM,+BAAiB,sMAAM,UAAU,CAGrC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,oKAAiB,OAAO;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,oKAAiB,OAAO,CAAC,WAAW"}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { VariantProps, cva } from \"class-variance-authority\"\nimport { PanelLeft } from \"lucide-react\"\n\nimport { useIsMobile } from \"@/hooks/use-mobile\"\nimport { cn } from \"@/lib/utils\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Sheet, SheetContent } from \"@/components/ui/sheet\"\nimport { Skeleton } from \"@/components/ui/skeleton\"\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\"\n\nconst SIDEBAR_COOKIE_NAME = \"sidebar:state\"\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\nconst SIDEBAR_WIDTH = \"16rem\"\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\n\ntype SidebarContext = {\n  state: \"expanded\" | \"collapsed\"\n  open: boolean\n  setOpen: (open: boolean) => void\n  openMobile: boolean\n  setOpenMobile: (open: boolean) => void\n  isMobile: boolean\n  toggleSidebar: () => void\n}\n\nconst SidebarContext = React.createContext<SidebarContext | null>(null)\n\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext)\n  if (!context) {\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\n  }\n\n  return context\n}\n\nconst SidebarProvider = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    defaultOpen?: boolean\n    open?: boolean\n    onOpenChange?: (open: boolean) => void\n  }\n>(\n  (\n    {\n      defaultOpen = true,\n      open: openProp,\n      onOpenChange: setOpenProp,\n      className,\n      style,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const isMobile = useIsMobile()\n    const [openMobile, setOpenMobile] = React.useState(false)\n\n    // This is the internal state of the sidebar.\n    // We use openProp and setOpenProp for control from outside the component.\n    const [_open, _setOpen] = React.useState(defaultOpen)\n    const open = openProp ?? _open\n    const setOpen = React.useCallback(\n      (value: boolean | ((value: boolean) => boolean)) => {\n        const openState = typeof value === \"function\" ? value(open) : value\n        if (setOpenProp) {\n          setOpenProp(openState)\n        } else {\n          _setOpen(openState)\n        }\n\n        // This sets the cookie to keep the sidebar state.\n        document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\n      },\n      [setOpenProp, open]\n    )\n\n    // Helper to toggle the sidebar.\n    const toggleSidebar = React.useCallback(() => {\n      return isMobile\n        ? setOpenMobile((open) => !open)\n        : setOpen((open) => !open)\n    }, [isMobile, setOpen, setOpenMobile])\n\n    // Adds a keyboard shortcut to toggle the sidebar.\n    React.useEffect(() => {\n      const handleKeyDown = (event: KeyboardEvent) => {\n        if (\n          event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\n          (event.metaKey || event.ctrlKey)\n        ) {\n          event.preventDefault()\n          toggleSidebar()\n        }\n      }\n\n      window.addEventListener(\"keydown\", handleKeyDown)\n      return () => window.removeEventListener(\"keydown\", handleKeyDown)\n    }, [toggleSidebar])\n\n    // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n    // This makes it easier to style the sidebar with Tailwind classes.\n    const state = open ? \"expanded\" : \"collapsed\"\n\n    const contextValue = React.useMemo<SidebarContext>(\n      () => ({\n        state,\n        open,\n        setOpen,\n        isMobile,\n        openMobile,\n        setOpenMobile,\n        toggleSidebar,\n      }),\n      [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\n    )\n\n    return (\n      <SidebarContext.Provider value={contextValue}>\n        <TooltipProvider delayDuration={0}>\n          <div\n            style={\n              {\n                \"--sidebar-width\": SIDEBAR_WIDTH,\n                \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\n                ...style,\n              } as React.CSSProperties\n            }\n            className={cn(\n              \"group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar\",\n              className\n            )}\n            ref={ref}\n            {...props}\n          >\n            {children}\n          </div>\n        </TooltipProvider>\n      </SidebarContext.Provider>\n    )\n  }\n)\nSidebarProvider.displayName = \"SidebarProvider\"\n\nconst Sidebar = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    side?: \"left\" | \"right\"\n    variant?: \"sidebar\" | \"floating\" | \"inset\"\n    collapsible?: \"offcanvas\" | \"icon\" | \"none\"\n  }\n>(\n  (\n    {\n      side = \"left\",\n      variant = \"sidebar\",\n      collapsible = \"offcanvas\",\n      className,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\n\n    if (collapsible === \"none\") {\n      return (\n        <div\n          className={cn(\n            \"flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground\",\n            className\n          )}\n          ref={ref}\n          {...props}\n        >\n          {children}\n        </div>\n      )\n    }\n\n    if (isMobile) {\n      return (\n        <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\n          <SheetContent\n            data-sidebar=\"sidebar\"\n            data-mobile=\"true\"\n            className=\"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden\"\n            style={\n              {\n                \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\n              } as React.CSSProperties\n            }\n            side={side}\n          >\n            <div className=\"flex h-full w-full flex-col\">{children}</div>\n          </SheetContent>\n        </Sheet>\n      )\n    }\n\n    return (\n      <div\n        ref={ref}\n        className=\"group peer hidden md:block text-sidebar-foreground\"\n        data-state={state}\n        data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\n        data-variant={variant}\n        data-side={side}\n      >\n        {/* This is what handles the sidebar gap on desktop */}\n        <div\n          className={cn(\n            \"duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear\",\n            \"group-data-[collapsible=offcanvas]:w-0\",\n            \"group-data-[side=right]:rotate-180\",\n            variant === \"floating\" || variant === \"inset\"\n              ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]\"\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon]\"\n          )}\n        />\n        <div\n          className={cn(\n            \"duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex\",\n            side === \"left\"\n              ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\n              : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\n            // Adjust the padding for floating and inset variants.\n            variant === \"floating\" || variant === \"inset\"\n              ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]\"\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l\",\n            className\n          )}\n          {...props}\n        >\n          <div\n            data-sidebar=\"sidebar\"\n            className=\"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow\"\n          >\n            {children}\n          </div>\n        </div>\n      </div>\n    )\n  }\n)\nSidebar.displayName = \"Sidebar\"\n\nconst SidebarTrigger = React.forwardRef<\n  React.ElementRef<typeof Button>,\n  React.ComponentProps<typeof Button>\n>(({ className, onClick, ...props }, ref) => {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <Button\n      ref={ref}\n      data-sidebar=\"trigger\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"h-7 w-7\", className)}\n      onClick={(event) => {\n        onClick?.(event)\n        toggleSidebar()\n      }}\n      {...props}\n    >\n      <PanelLeft />\n      <span className=\"sr-only\">Toggle Sidebar</span>\n    </Button>\n  )\n})\nSidebarTrigger.displayName = \"SidebarTrigger\"\n\nconst SidebarRail = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\">\n>(({ className, ...props }, ref) => {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <button\n      ref={ref}\n      data-sidebar=\"rail\"\n      aria-label=\"Toggle Sidebar\"\n      tabIndex={-1}\n      onClick={toggleSidebar}\n      title=\"Toggle Sidebar\"\n      className={cn(\n        \"absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex\",\n        \"[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize\",\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\n        \"group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar\",\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarRail.displayName = \"SidebarRail\"\n\nconst SidebarInset = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"main\">\n>(({ className, ...props }, ref) => {\n  return (\n    <main\n      ref={ref}\n      className={cn(\n        \"relative flex min-h-svh flex-1 flex-col bg-background\",\n        \"peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarInset.displayName = \"SidebarInset\"\n\nconst SidebarInput = React.forwardRef<\n  React.ElementRef<typeof Input>,\n  React.ComponentProps<typeof Input>\n>(({ className, ...props }, ref) => {\n  return (\n    <Input\n      ref={ref}\n      data-sidebar=\"input\"\n      className={cn(\n        \"h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarInput.displayName = \"SidebarInput\"\n\nconst SidebarHeader = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"header\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarHeader.displayName = \"SidebarHeader\"\n\nconst SidebarFooter = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"footer\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarFooter.displayName = \"SidebarFooter\"\n\nconst SidebarSeparator = React.forwardRef<\n  React.ElementRef<typeof Separator>,\n  React.ComponentProps<typeof Separator>\n>(({ className, ...props }, ref) => {\n  return (\n    <Separator\n      ref={ref}\n      data-sidebar=\"separator\"\n      className={cn(\"mx-2 w-auto bg-sidebar-border\", className)}\n      {...props}\n    />\n  )\n})\nSidebarSeparator.displayName = \"SidebarSeparator\"\n\nconst SidebarContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"content\"\n      className={cn(\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarContent.displayName = \"SidebarContent\"\n\nconst SidebarGroup = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"group\"\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarGroup.displayName = \"SidebarGroup\"\n\nconst SidebarGroupLabel = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & { asChild?: boolean }\n>(({ className, asChild = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"div\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"group-label\"\n      className={cn(\n        \"duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarGroupLabel.displayName = \"SidebarGroupLabel\"\n\nconst SidebarGroupAction = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & { asChild?: boolean }\n>(({ className, asChild = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"group-action\"\n      className={cn(\n        \"absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 after:md:hidden\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarGroupAction.displayName = \"SidebarGroupAction\"\n\nconst SidebarGroupContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    data-sidebar=\"group-content\"\n    className={cn(\"w-full text-sm\", className)}\n    {...props}\n  />\n))\nSidebarGroupContent.displayName = \"SidebarGroupContent\"\n\nconst SidebarMenu = React.forwardRef<\n  HTMLUListElement,\n  React.ComponentProps<\"ul\">\n>(({ className, ...props }, ref) => (\n  <ul\n    ref={ref}\n    data-sidebar=\"menu\"\n    className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\n    {...props}\n  />\n))\nSidebarMenu.displayName = \"SidebarMenu\"\n\nconst SidebarMenuItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentProps<\"li\">\n>(({ className, ...props }, ref) => (\n  <li\n    ref={ref}\n    data-sidebar=\"menu-item\"\n    className={cn(\"group/menu-item relative\", className)}\n    {...props}\n  />\n))\nSidebarMenuItem.displayName = \"SidebarMenuItem\"\n\nconst sidebarMenuButtonVariants = cva(\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n        outline:\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\n      },\n      size: {\n        default: \"h-8 text-sm\",\n        sm: \"h-7 text-xs\",\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:!p-0\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nconst SidebarMenuButton = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & {\n    asChild?: boolean\n    isActive?: boolean\n    tooltip?: string | React.ComponentProps<typeof TooltipContent>\n  } & VariantProps<typeof sidebarMenuButtonVariants>\n>(\n  (\n    {\n      asChild = false,\n      isActive = false,\n      variant = \"default\",\n      size = \"default\",\n      tooltip,\n      className,\n      ...props\n    },\n    ref\n  ) => {\n    const Comp = asChild ? Slot : \"button\"\n    const { isMobile, state } = useSidebar()\n\n    const button = (\n      <Comp\n        ref={ref}\n        data-sidebar=\"menu-button\"\n        data-size={size}\n        data-active={isActive}\n        className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\n        {...props}\n      />\n    )\n\n    if (!tooltip) {\n      return button\n    }\n\n    if (typeof tooltip === \"string\") {\n      tooltip = {\n        children: tooltip,\n      }\n    }\n\n    return (\n      <Tooltip>\n        <TooltipTrigger asChild>{button}</TooltipTrigger>\n        <TooltipContent\n          side=\"right\"\n          align=\"center\"\n          hidden={state !== \"collapsed\" || isMobile}\n          {...tooltip}\n        />\n      </Tooltip>\n    )\n  }\n)\nSidebarMenuButton.displayName = \"SidebarMenuButton\"\n\nconst SidebarMenuAction = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & {\n    asChild?: boolean\n    showOnHover?: boolean\n  }\n>(({ className, asChild = false, showOnHover = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"menu-action\"\n      className={cn(\n        \"absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 after:md:hidden\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        showOnHover &&\n          \"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarMenuAction.displayName = \"SidebarMenuAction\"\n\nconst SidebarMenuBadge = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    data-sidebar=\"menu-badge\"\n    className={cn(\n      \"absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none\",\n      \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\n      \"peer-data-[size=sm]/menu-button:top-1\",\n      \"peer-data-[size=default]/menu-button:top-1.5\",\n      \"peer-data-[size=lg]/menu-button:top-2.5\",\n      \"group-data-[collapsible=icon]:hidden\",\n      className\n    )}\n    {...props}\n  />\n))\nSidebarMenuBadge.displayName = \"SidebarMenuBadge\"\n\nconst SidebarMenuSkeleton = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    showIcon?: boolean\n  }\n>(({ className, showIcon = false, ...props }, ref) => {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`\n  }, [])\n\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"menu-skeleton\"\n      className={cn(\"rounded-md h-8 flex gap-2 px-2 items-center\", className)}\n      {...props}\n    >\n      {showIcon && (\n        <Skeleton\n          className=\"size-4 rounded-md\"\n          data-sidebar=\"menu-skeleton-icon\"\n        />\n      )}\n      <Skeleton\n        className=\"h-4 flex-1 max-w-[--skeleton-width]\"\n        data-sidebar=\"menu-skeleton-text\"\n        style={\n          {\n            \"--skeleton-width\": width,\n          } as React.CSSProperties\n        }\n      />\n    </div>\n  )\n})\nSidebarMenuSkeleton.displayName = \"SidebarMenuSkeleton\"\n\nconst SidebarMenuSub = React.forwardRef<\n  HTMLUListElement,\n  React.ComponentProps<\"ul\">\n>(({ className, ...props }, ref) => (\n  <ul\n    ref={ref}\n    data-sidebar=\"menu-sub\"\n    className={cn(\n      \"mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5\",\n      \"group-data-[collapsible=icon]:hidden\",\n      className\n    )}\n    {...props}\n  />\n))\nSidebarMenuSub.displayName = \"SidebarMenuSub\"\n\nconst SidebarMenuSubItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentProps<\"li\">\n>(({ ...props }, ref) => <li ref={ref} {...props} />)\nSidebarMenuSubItem.displayName = \"SidebarMenuSubItem\"\n\nconst SidebarMenuSubButton = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentProps<\"a\"> & {\n    asChild?: boolean\n    size?: \"sm\" | \"md\"\n    isActive?: boolean\n  }\n>(({ asChild = false, size = \"md\", isActive, className, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"menu-sub-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(\n        \"flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground\",\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\n        size === \"sm\" && \"text-xs\",\n        size === \"md\" && \"text-sm\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarMenuSubButton.displayName = \"SidebarMenuSubButton\"\n\nexport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupAction,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInput,\n  SidebarInset,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuBadge,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSkeleton,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarProvider,\n  SidebarRail,\n  SidebarSeparator,\n  SidebarTrigger,\n  useSidebar,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AAEA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;AAFA;AAHA;;;;;;;;;;;;;;AAqBA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,sMAAM,aAAa,CAAwB;AAElE,SAAS;IACP,MAAM,UAAU,sMAAM,UAAU,CAAC;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,MAAM,gCAAkB,sMAAM,UAAU,CAQtC,CACE,EACE,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,sMAAM,QAAQ,CAAC;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,sMAAM,QAAQ,CAAC;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,sMAAM,WAAW,CAC/B,CAAC;QACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;QAC9D,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,SAAS;QACX;QAEA,kDAAkD;QAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;IACpG,GACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,sMAAM,WAAW,CAAC;QACtC,OAAO,WACH,cAAc,CAAC,OAAS,CAAC,QACzB,QAAQ,CAAC,OAAS,CAAC;IACzB,GAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,sMAAM,SAAS,CAAC;QACd,MAAM,gBAAgB,CAAC;YACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;gBACA,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,sMAAM,OAAO,CAChC,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,8OAAC,mIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,8OAAC;gBACC,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;gBAEF,KAAK;gBACJ,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;AAEF,gBAAgB,WAAW,GAAG;AAE9B,MAAM,wBAAU,sMAAM,UAAU,CAQ9B,CACE,EACE,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,8OAAC;YACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAEF,KAAK;YACJ,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,8OAAC,iIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;0BAEN,cAAA,8OAAC;oBAAI,WAAU;8BAA+B;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;;0BAGX,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iGACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,yFACA;;;;;;0BAGR,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,kGACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,8OAAC;oBACC,gBAAa;oBACb,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;AAEF,QAAQ,WAAW,GAAG;AAEtB,MAAM,+BAAiB,sMAAM,UAAU,CAGrC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACnC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,KAAK;QACL,gBAAa;QACb,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;QACzB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,YAAS;;;;;0BACV,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mPACA,8EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,YAAY,WAAW,GAAG;AAE1B,MAAM,6BAAe,sMAAM,UAAU,CAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA,gRACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,6BAAe,sMAAM,UAAU,CAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AACA,cAAc,WAAW,GAAG;AAE5B,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AACA,cAAc,WAAW,GAAG;AAE5B,MAAM,iCAAmB,sMAAM,UAAU,CAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,qIAAA,CAAA,YAAS;QACR,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AACA,iBAAiB,WAAW,GAAG;AAE/B,MAAM,+BAAiB,sMAAM,UAAU,CAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,6BAAe,sMAAM,UAAU,CAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,kCAAoB,sMAAM,UAAU,CAGxC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sOACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,kBAAkB,WAAW,GAAG;AAEhC,MAAM,mCAAqB,sMAAM,UAAU,CAGzC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,mBAAmB,WAAW,GAAG;AAEjC,MAAM,oCAAsB,sMAAM,UAAU,CAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGb,oBAAoB,WAAW,GAAG;AAElC,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,gCAAkB,sMAAM,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAA4B,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAClC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,kCAAoB,sMAAM,UAAU,CAQxC,CACE,EACE,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OACJ,EACD;IAEA,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,mIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,8OAAC,mIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;AAEF,kBAAkB,WAAW,GAAG;AAEhC,MAAM,kCAAoB,sMAAM,UAAU,CAMxC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,cAAc,KAAK,EAAE,GAAG,OAAO,EAAE;IAChE,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;AACA,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,sMAAM,UAAU,CAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG;AAE/B,MAAM,oCAAsB,sMAAM,UAAU,CAK1C,CAAC,EAAE,SAAS,EAAE,WAAW,KAAK,EAAE,GAAG,OAAO,EAAE;IAC5C,kCAAkC;IAClC,MAAM,QAAQ,sMAAM,OAAO,CAAC;QAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;IAClD,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;AACA,oBAAoB,WAAW,GAAG;AAElC,MAAM,+BAAiB,sMAAM,UAAU,CAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,mCAAqB,sMAAM,UAAU,CAGzC,CAAC,EAAE,GAAG,OAAO,EAAE,oBAAQ,8OAAC;QAAG,KAAK;QAAM,GAAG,KAAK;;;;;;AAChD,mBAAmB,WAAW,GAAG;AAEjC,MAAM,qCAAuB,sMAAM,UAAU,CAO3C,CAAC,EAAE,UAAU,KAAK,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAClE,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+eACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG"}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1038, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/hooks/useAuth.ts"], "sourcesContent": ["import { useAuth0 } from '@auth0/auth0-react';\nimport { useMemo, useEffect, useState, useCallback } from 'react';\nimport { useRouter } from 'next/navigation';\n\n/**\n * Hook de autenticação simplificado que usa Auth0 como única fonte de verdade\n * Não usa localStorage - todos os dados vêm diretamente do Auth0\n */\nexport function useAuth() {\n  const {\n    user,\n    isAuthenticated,\n    isLoading,\n    loginWithRedirect,\n    logout,\n    getAccessTokenSilently,\n    getIdTokenClaims\n  } = useAuth0();\n\n  const [userRoles, setUserRoles] = useState<string[]>([]);\n  const [rolesLoaded, setRolesLoaded] = useState(false);\n  const [mounted, setMounted] = useState(false);\n  const router = useRouter();\n\n  // Garantir que só roda no cliente\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  // Buscar roles do usuário quando autenticado\n  useEffect(() => {\n    if (mounted && isAuthenticated && user && !rolesLoaded) {\n      fetchUserRoles();\n    }\n  }, [mounted, isAuthenticated, user, rolesLoaded]);\n\n  const fetchUserRoles = async () => {\n    try {\n      console.log('🔄 [AUTH] Buscando roles do usuário...');\n      \n      const token = await getAccessTokenSilently({\n        authorizationParams: {\n          audience: process.env.NEXT_PUBLIC_AUTH0_AUDIENCE,\n          scope: 'openid profile email offline_access'\n        }\n      });\n\n      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/verify`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setUserRoles(data.user.roles || []);\n        setRolesLoaded(true);\n        console.log('✅ [AUTH] Roles carregadas:', data.user.roles);\n\n        // Redirecionar para home se estiver na página de callback\n        if (window.location.pathname === '/callback') {\n          console.log('🔄 [AUTH] Redirecionando para home...');\n          router.push('/');\n        }\n      } else {\n        console.error('❌ [AUTH] Erro ao buscar roles:', response.status);\n        throw new Error(`Falha na verificação de roles: ${response.status}`);\n      }\n    } catch (error) {\n      console.error('❌ [AUTH] Erro ao buscar roles:', error);\n      \n      // Verificar se é erro de refresh token\n      if (error instanceof Error && (\n        error.message.includes('Missing Refresh Token') ||\n        error.message.includes('consent_required') ||\n        error.message.includes('login_required')\n      )) {\n        console.log('🔄 [AUTH] Refresh token expirado - fazendo logout...');\n        handleLogout();\n      } else {\n        // Para outros erros, definir estado para permitir re-login\n        setRolesLoaded(true);\n        setUserRoles([]);\n      }\n    }\n  };\n\n  // Verificar se o usuário tem role Admin\n  const isAdmin = useMemo(() => {\n    return userRoles.includes('Admin');\n  }, [userRoles]);\n\n  // Função para fazer login\n  const login = () => {\n    loginWithRedirect({\n      authorizationParams: {\n        redirect_uri: `${window.location.origin}/callback`\n      }\n    });\n  };\n\n  // Função de logout limpa\n  const handleLogout = () => {\n    // Limpar qualquer coisa do localStorage se necessário\n    localStorage.removeItem('user_data');\n    setUserRoles([]);\n    setRolesLoaded(false);\n    \n    logout({\n      logoutParams: {\n        returnTo: `${window.location.origin}/login`\n      }\n    });\n  };\n\n  // Função para obter token sempre do Auth0\n  const getToken = useCallback(async () => {\n    try {\n      console.log('🔍 [AUTH] Obtendo token do Auth0...');\n      \n      if (!isAuthenticated) {\n        console.log('❌ [AUTH] Usuário não está autenticado no Auth0');\n        return null;\n      }\n      \n      const token = await getAccessTokenSilently({\n        authorizationParams: {\n          audience: process.env.NEXT_PUBLIC_AUTH0_AUDIENCE,\n          scope: 'openid profile email offline_access'\n        }\n      });\n      \n      console.log('✅ [AUTH] Token obtido do Auth0:', token ? 'Sucesso' : 'Falhou');\n      return token;\n    } catch (error) {\n      console.error('❌ [AUTH] Erro ao obter token:', error);\n      \n      if (error instanceof Error && (\n        error.message?.includes('consent_required') || \n        error.message?.includes('login_required') ||\n        error.message?.includes('Missing Refresh Token')\n      )) {\n        console.log('🔄 [AUTH] Necessário fazer login novamente - fazendo logout...');\n        handleLogout();\n      }\n      \n      return null;\n    }\n  }, [isAuthenticated, getAccessTokenSilently, handleLogout]);\n\n  // Estados derivados usando dados do Auth0\n  const isLoggedIn = mounted && isAuthenticated && !!user;\n  const canAccess = isLoggedIn && isAdmin;\n  const needsLogin = mounted && !isLoggedIn && !isLoading;\n  const isReady = mounted && (!isAuthenticated || (isAuthenticated && rolesLoaded));\n\n  return {\n    // Dados do usuário diretamente do Auth0\n    user: mounted ? user : null,\n    isAuthenticated: isLoggedIn,\n    isLoading: !mounted || isLoading || (isAuthenticated && !rolesLoaded),\n    \n    // Verificações\n    isAdmin,\n    canAccess,\n    needsLogin,\n    userRoles,\n    isReady, // 🔑 Flag principal para saber quando pode fazer requisições\n    \n    // Funções\n    login,\n    logout: handleLogout,\n    getToken,\n    getIdTokenClaims\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAMO,SAAS;IACd,MAAM,EACJ,IAAI,EACJ,eAAe,EACf,SAAS,EACT,iBAAiB,EACjB,MAAM,EACN,sBAAsB,EACtB,gBAAgB,EACjB,GAAG,CAAA,GAAA,0KAAA,CAAA,WAAQ,AAAD;IAEX,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,mBAAmB,QAAQ,CAAC,aAAa;YACtD;QACF;IACF,GAAG;QAAC;QAAS;QAAiB;QAAM;KAAY;IAEhD,MAAM,iBAAiB;QACrB,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,QAAQ,MAAM,uBAAuB;gBACzC,qBAAqB;oBACnB,QAAQ;oBACR,OAAO;gBACT;YACF;YAEA,MAAM,WAAW,MAAM,MAAM,6DAAwC,gBAAgB,CAAC,EAAE;gBACtF,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,aAAa,KAAK,IAAI,CAAC,KAAK,IAAI,EAAE;gBAClC,eAAe;gBACf,QAAQ,GAAG,CAAC,8BAA8B,KAAK,IAAI,CAAC,KAAK;gBAEzD,0DAA0D;gBAC1D,IAAI,OAAO,QAAQ,CAAC,QAAQ,KAAK,aAAa;oBAC5C,QAAQ,GAAG,CAAC;oBACZ,OAAO,IAAI,CAAC;gBACd;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC,kCAAkC,SAAS,MAAM;gBAC/D,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,SAAS,MAAM,EAAE;YACrE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAEhD,uCAAuC;YACvC,IAAI,iBAAiB,SAAS,CAC5B,MAAM,OAAO,CAAC,QAAQ,CAAC,4BACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,uBACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,iBACzB,GAAG;gBACD,QAAQ,GAAG,CAAC;gBACZ;YACF,OAAO;gBACL,2DAA2D;gBAC3D,eAAe;gBACf,aAAa,EAAE;YACjB;QACF;IACF;IAEA,wCAAwC;IACxC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACtB,OAAO,UAAU,QAAQ,CAAC;IAC5B,GAAG;QAAC;KAAU;IAEd,0BAA0B;IAC1B,MAAM,QAAQ;QACZ,kBAAkB;YAChB,qBAAqB;gBACnB,cAAc,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC;YACpD;QACF;IACF;IAEA,yBAAyB;IACzB,MAAM,eAAe;QACnB,sDAAsD;QACtD,aAAa,UAAU,CAAC;QACxB,aAAa,EAAE;QACf,eAAe;QAEf,OAAO;YACL,cAAc;gBACZ,UAAU,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;YAC7C;QACF;IACF;IAEA,0CAA0C;IAC1C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3B,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,IAAI,CAAC,iBAAiB;gBACpB,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACT;YAEA,MAAM,QAAQ,MAAM,uBAAuB;gBACzC,qBAAqB;oBACnB,QAAQ;oBACR,OAAO;gBACT;YACF;YAEA,QAAQ,GAAG,CAAC,mCAAmC,QAAQ,YAAY;YACnE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAE/C,IAAI,iBAAiB,SAAS,CAC5B,MAAM,OAAO,EAAE,SAAS,uBACxB,MAAM,OAAO,EAAE,SAAS,qBACxB,MAAM,OAAO,EAAE,SAAS,wBAC1B,GAAG;gBACD,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,OAAO;QACT;IACF,GAAG;QAAC;QAAiB;QAAwB;KAAa;IAE1D,0CAA0C;IAC1C,MAAM,aAAa,WAAW,mBAAmB,CAAC,CAAC;IACnD,MAAM,YAAY,cAAc;IAChC,MAAM,aAAa,WAAW,CAAC,cAAc,CAAC;IAC9C,MAAM,UAAU,WAAW,CAAC,CAAC,mBAAoB,mBAAmB,WAAY;IAEhF,OAAO;QACL,wCAAwC;QACxC,MAAM,UAAU,OAAO;QACvB,iBAAiB;QACjB,WAAW,CAAC,WAAW,aAAc,mBAAmB,CAAC;QAEzD,eAAe;QACf;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;QACA,QAAQ;QACR;QACA;IACF;AACF"}}, {"offset": {"line": 1188, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1194, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/ui/user-profile.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useRef, useEffect } from 'react';\r\nimport { useAuth } from '@/hooks/useAuth';\r\nimport { LogOut, User, Settings, ChevronDown } from 'lucide-react';\r\n\r\ninterface UserProfileProps {\r\n  showName?: boolean;\r\n  size?: 'sm' | 'md' | 'lg';\r\n}\r\n\r\nexport function UserProfile({ showName = true, size = 'md' }: UserProfileProps) {\r\n  const { user, logout } = useAuth();\r\n  const [showDropdown, setShowDropdown] = useState(false);\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Fechar dropdown quando clicar fora\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setShowDropdown(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => document.removeEventListener('mousedown', handleClickOutside);\r\n  }, []);\r\n\r\n  const handleLogout = () => {\r\n    setShowDropdown(false);\r\n    logout();\r\n  };\r\n\r\n  const avatarSizes = {\r\n    sm: 'w-8 h-8',\r\n    md: 'w-10 h-10',\r\n    lg: 'w-12 h-12'\r\n  };\r\n\r\n  const textSizes = {\r\n    sm: 'text-sm',\r\n    md: 'text-base',\r\n    lg: 'text-lg'\r\n  };\r\n\r\n  if (!user) {\r\n    return null;\r\n  }\r\n\r\n      return (\r\n      <div className=\"relative\" ref={dropdownRef}>\r\n      <button\r\n        onClick={() => setShowDropdown(!showDropdown)}\r\n        className=\"w-full flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n      >\r\n        <img\r\n          src={user.picture || '/default-avatar.png'}\r\n          alt={user.name || 'Usuário'}\r\n          className={`${avatarSizes[size]} rounded-full object-cover border-2 border-gray-200`}\r\n          referrerPolicy=\"no-referrer\"\r\n        />\r\n        \r\n        {showName && (\r\n          <div className=\"flex items-center space-x-2\">\r\n            <div className=\"text-left\">\r\n              <p className={`${textSizes[size]} font-medium text-gray-900 truncate max-w-32`}>\r\n                {user.name || 'Usuário'}\r\n              </p>\r\n              <p className=\"text-sm text-gray-500 truncate max-w-32\">\r\n                {user.email}\r\n              </p>\r\n            </div>\r\n            <ChevronDown \r\n              className={`w-4 h-4 text-gray-500 transition-transform ${showDropdown ? 'rotate-180' : ''}`} \r\n            />\r\n          </div>\r\n        )}\r\n      </button>\r\n\r\n      {showDropdown && (\r\n        <div className=\"absolute bottom-full left-0 mb-2 w-72 bg-white rounded-lg shadow-lg border border-gray-200 z-[100]\">\r\n          {/* Header do dropdown */}\r\n          <div className=\"p-4 border-b border-gray-100\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <img\r\n                src={user.picture || '/default-avatar.png'}\r\n                alt={user.name || 'Usuário'}\r\n                className=\"w-12 h-12 rounded-full object-cover border-2 border-gray-200\"\r\n                referrerPolicy=\"no-referrer\"\r\n              />\r\n              <div className=\"flex-1 min-w-0\">\r\n                <h4 className=\"text-lg font-semibold text-gray-900 truncate\">\r\n                  {user.name || 'Usuário'}\r\n                </h4>\r\n                <p className=\"text-sm text-gray-500 truncate\">\r\n                  {user.email}\r\n                </p>\r\n                {user.email_verified && (\r\n                  <span className=\"inline-flex items-center px-2 py-1 mt-1 rounded-full text-xs font-medium bg-green-100 text-green-800\">\r\n                    ✓ Verificado\r\n                  </span>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Menu do dropdown */}\r\n          <div className=\"py-2\">\r\n            <button\r\n              onClick={() => {\r\n                setShowDropdown(false);\r\n                // Aqui você pode adicionar navegação para perfil\r\n              }}\r\n              className=\"w-full flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors\"\r\n            >\r\n              <User className=\"w-5 h-5 mr-3 text-gray-400\" />\r\n              <span>Meu Perfil</span>\r\n            </button>\r\n            \r\n            <button\r\n              onClick={() => {\r\n                setShowDropdown(false);\r\n                // Aqui você pode adicionar navegação para configurações\r\n              }}\r\n              className=\"w-full flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors\"\r\n            >\r\n              <Settings className=\"w-5 h-5 mr-3 text-gray-400\" />\r\n              <span>Configurações</span>\r\n            </button>\r\n\r\n            <div className=\"border-t border-gray-100 mt-2 pt-2\">\r\n              <button\r\n                onClick={handleLogout}\r\n                className=\"w-full flex items-center px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors\"\r\n              >\r\n                <LogOut className=\"w-5 h-5 mr-3 text-red-500\" />\r\n                <span>Sair</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAWO,SAAS,YAAY,EAAE,WAAW,IAAI,EAAE,OAAO,IAAI,EAAoB;IAC5E,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,gBAAgB;YAClB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,gBAAgB;QAChB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,YAAY;QAChB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEI,qBACA,8OAAC;QAAI,WAAU;QAAW,KAAK;;0BAC/B,8OAAC;gBACC,SAAS,IAAM,gBAAgB,CAAC;gBAChC,WAAU;;kCAEV,8OAAC;wBACC,KAAK,KAAK,OAAO,IAAI;wBACrB,KAAK,KAAK,IAAI,IAAI;wBAClB,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,mDAAmD,CAAC;wBACpF,gBAAe;;;;;;oBAGhB,0BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAW,GAAG,SAAS,CAAC,KAAK,CAAC,4CAA4C,CAAC;kDAC3E,KAAK,IAAI,IAAI;;;;;;kDAEhB,8OAAC;wCAAE,WAAU;kDACV,KAAK,KAAK;;;;;;;;;;;;0CAGf,8OAAC,oNAAA,CAAA,cAAW;gCACV,WAAW,CAAC,2CAA2C,EAAE,eAAe,eAAe,IAAI;;;;;;;;;;;;;;;;;;YAMlG,8BACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,KAAK,KAAK,OAAO,IAAI;oCACrB,KAAK,KAAK,IAAI,IAAI;oCAClB,WAAU;oCACV,gBAAe;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,KAAK,IAAI,IAAI;;;;;;sDAEhB,8OAAC;4CAAE,WAAU;sDACV,KAAK,KAAK;;;;;;wCAEZ,KAAK,cAAc,kBAClB,8OAAC;4CAAK,WAAU;sDAAuG;;;;;;;;;;;;;;;;;;;;;;;kCAS/H,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;oCACP,gBAAgB;gCAChB,iDAAiD;gCACnD;gCACA,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;0CAGR,8OAAC;gCACC,SAAS;oCACP,gBAAgB;gCAChB,wDAAwD;gCAC1D;gCACA,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;0CAGR,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB"}}, {"offset": {"line": 1474, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1480, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/app-sidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport {\n    Calendar,\n    Home,\n    Inbox,\n    Search,\n    Settings,\n    ChevronUp,\n    Server,\n    ChevronDown,\n    Building2,\n    Mail,\n    Users,\n    Plus,\n    List,\n    Cloud,\n    FileText\n} from \"lucide-react\";\nimport { IoServer } from \"react-icons/io5\";\nimport { cn } from \"@/lib/utils\";\nimport { Button } from \"@/components/ui/button\";\n\nimport {\n    Sidebar,\n    SidebarContent,\n    SidebarGroup,\n    SidebarGroupContent,\n    SidebarGroupLabel,\n    SidebarMenu,\n    SidebarMenuButton,\n    SidebarMenuItem,\n    SidebarFooter\n} from \"@/components/ui/sidebar\";\n\n// Removido DropdownMenu - usando UserProfile agora\n\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { UserProfile } from \"@/components/ui/user-profile\";\n\n// Menu items principais.\nconst mainItems = [\n    { title: \"Home\", url: \"/\", icon: Home },\n    /*{ title: \"Aplicativos\", url: \"/list-apps\", icon: Inbox },\n    { title: \"Search\", url: \"#\", icon: Search },\n    { title: \"Configurações\", url: \"#\", icon: Settings },*/\n];\n\nexport function AppSidebar() {\n    const [companiesOpen, setCompaniesOpen] = useState(false);\n    const [serversOpen, setServersOpen] = useState(false);\n    const [schedulesOpen, setSchedulesOpen] = useState(false);\n    const [metabaseOpen, setMetabaseOpen] = useState(false);\n    const [smtpOpen, setSmtpOpen] = useState(false);\n    const [recipientsOpen, setRecipientsOpen] = useState(false);\n    const [nprintingOpen, setNprintingOpen] = useState(false);\n    const [qlikCloudOpen, setQlikCloudOpen] = useState(false);\n    const [qlikEnterpriseOpen, setQlikEnterpriseOpen] = useState(false);\n\n    // Hook Auth0 - não precisamos mais extrair user e logout aqui\n    // const { user, logout } = useAuth();\n\n    return (\n        <Sidebar>\n            <SidebarContent>\n                <SidebarGroup>\n                    <SidebarGroupLabel>Soeva - Soluções Inteligentes</SidebarGroupLabel>\n                    <SidebarGroupContent>\n                        <SidebarMenu>\n                            {mainItems.map((item) => (\n                                <SidebarMenuItem key={item.title}>\n                                    <SidebarMenuButton asChild>\n                                        <a href={item.url}>\n                                            <item.icon />\n                                            <span>{item.title}</span>\n                                        </a>\n                                    </SidebarMenuButton>\n                                </SidebarMenuItem>\n                            ))}\n\n                            {/* Menu de Empresas */}\n                            <SidebarMenuItem>\n                                <SidebarMenuButton onClick={() => setCompaniesOpen(!companiesOpen)} className={cn(\n                                    \"flex justify-between w-full\",\n                                    companiesOpen && \"bg-accent\"\n                                )}>\n                                    <div className=\"flex items-center\">\n                                        <Building2 className=\"mr-2\" width={16} />\n                                        <span>Empresas</span>\n                                    </div>\n                                    {companiesOpen ? <ChevronUp /> : <ChevronDown />}\n                                </SidebarMenuButton>\n                                {companiesOpen && (\n                                    <div className=\"ml-6 mt-2 space-y-2 bg-accent/50 rounded-md p-1\">\n                                        <SidebarMenuButton asChild>\n                                            <a href=\"/company/add\" className=\"flex items-center\">\n                                                <Plus className=\"mr-2 h-4 w-4\" />\n                                                <span>Adicionar</span>\n                                            </a>\n                                        </SidebarMenuButton>\n                                        <SidebarMenuButton asChild>\n                                            <a href=\"/company/list\" className=\"flex items-center\">\n                                                <List className=\"mr-2 h-4 w-4\" />\n                                                <span>Listar</span>\n                                            </a>\n                                        </SidebarMenuButton>\n                                    </div>\n                                )}\n                            </SidebarMenuItem>                            \n\n                            {/* Menu de Servidores */}\n                            <SidebarMenuItem>\n                                <SidebarMenuButton onClick={() => setServersOpen(!serversOpen)} className={cn(\n                                    \"flex justify-between w-full\",\n                                    serversOpen && \"bg-accent\"\n                                )}>\n                                    <div className=\"flex items-center\">\n                                        <Server className=\"mr-2\" width={16} />\n                                        <span>Servidores</span>\n                                    </div>\n                                    {serversOpen ? <ChevronUp /> : <ChevronDown />}\n                                </SidebarMenuButton>\n                                {serversOpen && (\n                                    <div className=\"ml-6 mt-2 space-y-2\">\n                                        {/* Submenu Qlik Sense Enterprise */}\n                                        <div>\n                                            <SidebarMenuButton onClick={() => setQlikEnterpriseOpen(!qlikEnterpriseOpen)} className={cn(\n                                                \"flex justify-between w-full min-h-[45px]\",\n                                                qlikEnterpriseOpen && \"bg-accent\"\n                                            )}>\n                                                <div className=\"flex items-center\">\n                                                    <Server className=\"mr-2\" width={16} />\n                                                    <span>Qlik Sense Enterprise Client Manager</span>\n                                                </div>\n                                                {qlikEnterpriseOpen ? <ChevronUp /> : <ChevronDown />}\n                                            </SidebarMenuButton>\n                                            {qlikEnterpriseOpen && (\n                                                <div className=\"ml-6 mt-2 space-y-2 bg-accent/50 rounded-md p-1\">\n                                                    <SidebarMenuButton asChild>\n                                                        <a href=\"/servers/add\" className=\"flex items-center\">\n                                                            <Plus className=\"mr-2 h-4 w-4\" />\n                                                            <span>Adicionar</span>\n                                                        </a>\n                                                    </SidebarMenuButton>\n                                                    <SidebarMenuButton asChild>\n                                                        <a href=\"/servers/list\" className=\"flex items-center\">\n                                                            <List className=\"mr-2 h-4 w-4\" />\n                                                            <span>Listar</span>\n                                                        </a>\n                                                    </SidebarMenuButton>\n                                                </div>\n                                            )}\n                                        </div>\n\n                                        {/* Submenu Qlik Cloud */}\n                                        <div>\n                                            <SidebarMenuButton onClick={() => setQlikCloudOpen(!qlikCloudOpen)} className={cn(\n                                                \"flex justify-between w-full\",\n                                                qlikCloudOpen && \"bg-accent\"\n                                            )}>\n                                                <div className=\"flex items-center\">\n                                                    <Cloud className=\"mr-2\" width={16} />\n                                                    <span>Qlik Sense Cloud</span>\n                                                </div>\n                                                {qlikCloudOpen ? <ChevronUp /> : <ChevronDown />}\n                                            </SidebarMenuButton>\n                                            {qlikCloudOpen && (\n                                                <div className=\"ml-6 mt-2 space-y-2 bg-accent/50 rounded-md p-1\">\n                                                    <SidebarMenuButton asChild>\n                                                        <a href=\"/qlikCloudServers/add\" className=\"flex items-center\">\n                                                            <Plus className=\"mr-2 h-4 w-4\" />\n                                                            <span>Adicionar</span>\n                                                        </a>\n                                                    </SidebarMenuButton>\n                                                    <SidebarMenuButton asChild>\n                                                        <a href=\"/qlikCloudServers/list\" className=\"flex items-center\">\n                                                            <List className=\"mr-2 h-4 w-4\" />\n                                                            <span>Listar</span>\n                                                        </a>\n                                                    </SidebarMenuButton>\n                                                </div>\n                                            )}\n                                        </div>\n                                    </div>\n                                )}\n                            </SidebarMenuItem>                             \n\n                            {/* Menu NPrinting */}\n                            <SidebarMenuItem>\n                                <SidebarMenuButton onClick={() => setNprintingOpen(!nprintingOpen)} className={cn(\n                                    \"flex justify-between w-full\",\n                                    nprintingOpen && \"bg-accent\"\n                                )}>\n                                    <div className=\"flex items-center\">\n                                        <IoServer className=\"mr-2 h-4 w-4\" />\n                                        <span>NPrinting</span>\n                                    </div>\n                                    {nprintingOpen ? <ChevronUp /> : <ChevronDown />}\n                                </SidebarMenuButton>\n                                {nprintingOpen && (\n                                    <div className=\"ml-6 mt-2 space-y-2 bg-accent/50 rounded-md p-1\">\n                                        <SidebarMenuButton asChild>\n                                            <a href=\"/nprinting/add\" className=\"flex items-center\">\n                                                <Plus className=\"mr-2 h-4 w-4\" />\n                                                <span>Adicionar</span>\n                                            </a>\n                                        </SidebarMenuButton>\n                                        <SidebarMenuButton asChild>\n                                            <a href=\"/nprinting/list\" className=\"flex items-center\">\n                                                <List className=\"mr-2 h-4 w-4\" />\n                                                <span>Listar</span>\n                                            </a>\n                                        </SidebarMenuButton>\n                                    </div>\n                                )}\n                            </SidebarMenuItem>                       \n\n                            {/* Menu de SMTP */}\n                            <SidebarMenuItem>\n                                <SidebarMenuButton onClick={() => setSmtpOpen(!smtpOpen)} className={cn(\n                                    \"flex justify-between w-full\",\n                                    smtpOpen && \"bg-accent\"\n                                )}>\n                                    <div className=\"flex items-center\">\n                                        <Mail className=\"mr-2\" width={16} />\n                                        <span>SMTP</span>\n                                    </div>\n                                    {smtpOpen ? <ChevronUp /> : <ChevronDown />}\n                                </SidebarMenuButton>\n                                {smtpOpen && (\n                                    <div className=\"ml-6 mt-2 space-y-2 bg-accent/50 rounded-md p-1\">\n                                        <SidebarMenuButton asChild>\n                                            <a href=\"/smtp/add\" className=\"flex items-center\">\n                                                <Plus className=\"mr-2 h-4 w-4\" />\n                                                <span>Adicionar</span>\n                                            </a>\n                                        </SidebarMenuButton>\n                                        <SidebarMenuButton asChild>\n                                            <a href=\"/smtp/list\" className=\"flex items-center\">\n                                                <List className=\"mr-2 h-4 w-4\" />\n                                                <span>Listar</span>\n                                            </a>\n                                        </SidebarMenuButton>\n                                    </div>\n                                )}\n                            </SidebarMenuItem>\n\n                            {/* Menu de Destinatários */}\n                            <SidebarMenuItem>\n                                <SidebarMenuButton onClick={() => setRecipientsOpen(!recipientsOpen)} className={cn(\n                                    \"flex justify-between w-full\",\n                                    recipientsOpen && \"bg-accent\"\n                                )}>\n                                    <div className=\"flex items-center\">\n                                        <Users className=\"mr-2\" width={16} />\n                                        <span>Destinatários</span>\n                                    </div>\n                                    {recipientsOpen ? <ChevronUp /> : <ChevronDown />}\n                                </SidebarMenuButton>\n                                {recipientsOpen && (\n                                    <div className=\"ml-6 mt-2 space-y-2 bg-accent/50 rounded-md p-1\">\n                                        <SidebarMenuButton asChild>\n                                            <a href=\"/recipients/add\" className=\"flex items-center\">\n                                                <Plus className=\"mr-2 h-4 w-4\" />\n                                                <span>Adicionar</span>\n                                            </a>\n                                        </SidebarMenuButton>\n                                        <SidebarMenuButton asChild>\n                                            <a href=\"/recipients/list\" className=\"flex items-center\">\n                                                <List className=\"mr-2 h-4 w-4\" />\n                                                <span>Listar</span>\n                                            </a>\n                                        </SidebarMenuButton>\n                                    </div>\n                                )}\n                            </SidebarMenuItem>       \n\n                            {/* Menu de Agendamentos */}\n                            <SidebarMenuItem>\n                                <SidebarMenuButton onClick={() => setMetabaseOpen(!metabaseOpen)} className={cn(\n                                    \"flex justify-between w-full\",\n                                    metabaseOpen && \"bg-accent\"\n                                )}>\n                                    <div className=\"flex items-center\">\n                                        <FileText className=\"mr-2\" width={16} />\n                                        <span>Relatórios</span>\n                                    </div>\n                                    {metabaseOpen ? <ChevronUp /> : <ChevronDown />}\n                                </SidebarMenuButton>\n                                {metabaseOpen && (\n                                    <div className=\"ml-6 mt-2 space-y-2 bg-accent/50 rounded-md p-1\">\n                                        <SidebarMenuButton asChild>\n                                            <a href=\"/reports/create\" className=\"block text-sm\">\n                                                <Plus className=\"mr-2 h-4 w-4\" />\n                                                <span>Criar Relatório</span>\n                                            </a>\n                                        </SidebarMenuButton>                                        \n                                    </div>\n                                )}\n                            </SidebarMenuItem>                     \n\n                            {/* Menu de Agendamentos */}\n                            <SidebarMenuItem>\n                                <SidebarMenuButton onClick={() => setSchedulesOpen(!schedulesOpen)} className={cn(\n                                    \"flex justify-between w-full\",\n                                    schedulesOpen && \"bg-accent\"\n                                )}>\n                                    <div className=\"flex items-center\">\n                                        <Calendar className=\"mr-2\" width={16} />\n                                        <span>Agendamentos</span>\n                                    </div>\n                                    {schedulesOpen ? <ChevronUp /> : <ChevronDown />}\n                                </SidebarMenuButton>\n                                {schedulesOpen && (\n                                    <div className=\"ml-6 mt-2 space-y-2 bg-accent/50 rounded-md p-1\">\n                                        <SidebarMenuButton asChild>\n                                            <a href=\"/schedules/add\" className=\"block text-sm\">\n                                                <Plus className=\"mr-2 h-4 w-4\" />\n                                                <span>Adicionar</span>\n                                            </a>\n                                        </SidebarMenuButton>\n                                        <SidebarMenuButton asChild>\n                                            <a href=\"/schedules/list\" className=\"flex items-center\">\n                                                <List className=\"mr-2 h-4 w-4\" />\n                                                <span>Listar</span>\n                                            </a>\n                                        </SidebarMenuButton>\n                                    </div>\n                                )}\n                            </SidebarMenuItem>\n\n                        </SidebarMenu>\n                    </SidebarGroupContent>\n                </SidebarGroup>\n            </SidebarContent>\n            <SidebarFooter>\n                <SidebarMenu>\n                    <SidebarMenuItem>\n                        <UserProfile size=\"md\" showName={true} />\n                    </SidebarMenuItem>\n                </SidebarMenu>\n            </SidebarFooter>\n        </Sidebar>\n    );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAmBA;AAGA;AAeA;AApCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AAjBA;AAAA;AAAA;AAAA;AAHA;;;;;;;;AAyCA,yBAAyB;AACzB,MAAM,YAAY;IACd;QAAE,OAAO;QAAQ,KAAK;QAAK,MAAM,mMAAA,CAAA,OAAI;IAAC;CAIzC;AAEM,SAAS;IACZ,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,8DAA8D;IAC9D,sCAAsC;IAEtC,qBACI,8OAAC,mIAAA,CAAA,UAAO;;0BACJ,8OAAC,mIAAA,CAAA,iBAAc;0BACX,cAAA,8OAAC,mIAAA,CAAA,eAAY;;sCACT,8OAAC,mIAAA,CAAA,oBAAiB;sCAAC;;;;;;sCACnB,8OAAC,mIAAA,CAAA,sBAAmB;sCAChB,cAAA,8OAAC,mIAAA,CAAA,cAAW;;oCACP,UAAU,GAAG,CAAC,CAAC,qBACZ,8OAAC,mIAAA,CAAA,kBAAe;sDACZ,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;gDAAC,OAAO;0DACtB,cAAA,8OAAC;oDAAE,MAAM,KAAK,GAAG;;sEACb,8OAAC,KAAK,IAAI;;;;;sEACV,8OAAC;sEAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;2CAJP,KAAK,KAAK;;;;;kDAWpC,8OAAC,mIAAA,CAAA,kBAAe;;0DACZ,8OAAC,mIAAA,CAAA,oBAAiB;gDAAC,SAAS,IAAM,iBAAiB,CAAC;gDAAgB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAC5E,+BACA,iBAAiB;;kEAEjB,8OAAC;wDAAI,WAAU;;0EACX,8OAAC,gNAAA,CAAA,YAAS;gEAAC,WAAU;gEAAO,OAAO;;;;;;0EACnC,8OAAC;0EAAK;;;;;;;;;;;;oDAET,8BAAgB,8OAAC,gNAAA,CAAA,YAAS;;;;6EAAM,8OAAC,oNAAA,CAAA,cAAW;;;;;;;;;;;4CAEhD,+BACG,8OAAC;gDAAI,WAAU;;kEACX,8OAAC,mIAAA,CAAA,oBAAiB;wDAAC,OAAO;kEACtB,cAAA,8OAAC;4DAAE,MAAK;4DAAe,WAAU;;8EAC7B,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;8EAAK;;;;;;;;;;;;;;;;;kEAGd,8OAAC,mIAAA,CAAA,oBAAiB;wDAAC,OAAO;kEACtB,cAAA,8OAAC;4DAAE,MAAK;4DAAgB,WAAU;;8EAC9B,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ1B,8OAAC,mIAAA,CAAA,kBAAe;;0DACZ,8OAAC,mIAAA,CAAA,oBAAiB;gDAAC,SAAS,IAAM,eAAe,CAAC;gDAAc,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACxE,+BACA,eAAe;;kEAEf,8OAAC;wDAAI,WAAU;;0EACX,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;gEAAO,OAAO;;;;;;0EAChC,8OAAC;0EAAK;;;;;;;;;;;;oDAET,4BAAc,8OAAC,gNAAA,CAAA,YAAS;;;;6EAAM,8OAAC,oNAAA,CAAA,cAAW;;;;;;;;;;;4CAE9C,6BACG,8OAAC;gDAAI,WAAU;;kEAEX,8OAAC;;0EACG,8OAAC,mIAAA,CAAA,oBAAiB;gEAAC,SAAS,IAAM,sBAAsB,CAAC;gEAAqB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACtF,4CACA,sBAAsB;;kFAEtB,8OAAC;wEAAI,WAAU;;0FACX,8OAAC,sMAAA,CAAA,SAAM;gFAAC,WAAU;gFAAO,OAAO;;;;;;0FAChC,8OAAC;0FAAK;;;;;;;;;;;;oEAET,mCAAqB,8OAAC,gNAAA,CAAA,YAAS;;;;6FAAM,8OAAC,oNAAA,CAAA,cAAW;;;;;;;;;;;4DAErD,oCACG,8OAAC;gEAAI,WAAU;;kFACX,8OAAC,mIAAA,CAAA,oBAAiB;wEAAC,OAAO;kFACtB,cAAA,8OAAC;4EAAE,MAAK;4EAAe,WAAU;;8FAC7B,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;8FAChB,8OAAC;8FAAK;;;;;;;;;;;;;;;;;kFAGd,8OAAC,mIAAA,CAAA,oBAAiB;wEAAC,OAAO;kFACtB,cAAA,8OAAC;4EAAE,MAAK;4EAAgB,WAAU;;8FAC9B,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;8FAChB,8OAAC;8FAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAQ1B,8OAAC;;0EACG,8OAAC,mIAAA,CAAA,oBAAiB;gEAAC,SAAS,IAAM,iBAAiB,CAAC;gEAAgB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAC5E,+BACA,iBAAiB;;kFAEjB,8OAAC;wEAAI,WAAU;;0FACX,8OAAC,oMAAA,CAAA,QAAK;gFAAC,WAAU;gFAAO,OAAO;;;;;;0FAC/B,8OAAC;0FAAK;;;;;;;;;;;;oEAET,8BAAgB,8OAAC,gNAAA,CAAA,YAAS;;;;6FAAM,8OAAC,oNAAA,CAAA,cAAW;;;;;;;;;;;4DAEhD,+BACG,8OAAC;gEAAI,WAAU;;kFACX,8OAAC,mIAAA,CAAA,oBAAiB;wEAAC,OAAO;kFACtB,cAAA,8OAAC;4EAAE,MAAK;4EAAwB,WAAU;;8FACtC,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;8FAChB,8OAAC;8FAAK;;;;;;;;;;;;;;;;;kFAGd,8OAAC,mIAAA,CAAA,oBAAiB;wEAAC,OAAO;kFACtB,cAAA,8OAAC;4EAAE,MAAK;4EAAyB,WAAU;;8FACvC,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;8FAChB,8OAAC;8FAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAWtC,8OAAC,mIAAA,CAAA,kBAAe;;0DACZ,8OAAC,mIAAA,CAAA,oBAAiB;gDAAC,SAAS,IAAM,iBAAiB,CAAC;gDAAgB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAC5E,+BACA,iBAAiB;;kEAEjB,8OAAC;wDAAI,WAAU;;0EACX,8OAAC,+IAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAK;;;;;;;;;;;;oDAET,8BAAgB,8OAAC,gNAAA,CAAA,YAAS;;;;6EAAM,8OAAC,oNAAA,CAAA,cAAW;;;;;;;;;;;4CAEhD,+BACG,8OAAC;gDAAI,WAAU;;kEACX,8OAAC,mIAAA,CAAA,oBAAiB;wDAAC,OAAO;kEACtB,cAAA,8OAAC;4DAAE,MAAK;4DAAiB,WAAU;;8EAC/B,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;8EAAK;;;;;;;;;;;;;;;;;kEAGd,8OAAC,mIAAA,CAAA,oBAAiB;wDAAC,OAAO;kEACtB,cAAA,8OAAC;4DAAE,MAAK;4DAAkB,WAAU;;8EAChC,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ1B,8OAAC,mIAAA,CAAA,kBAAe;;0DACZ,8OAAC,mIAAA,CAAA,oBAAiB;gDAAC,SAAS,IAAM,YAAY,CAAC;gDAAW,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAClE,+BACA,YAAY;;kEAEZ,8OAAC;wDAAI,WAAU;;0EACX,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;gEAAO,OAAO;;;;;;0EAC9B,8OAAC;0EAAK;;;;;;;;;;;;oDAET,yBAAW,8OAAC,gNAAA,CAAA,YAAS;;;;6EAAM,8OAAC,oNAAA,CAAA,cAAW;;;;;;;;;;;4CAE3C,0BACG,8OAAC;gDAAI,WAAU;;kEACX,8OAAC,mIAAA,CAAA,oBAAiB;wDAAC,OAAO;kEACtB,cAAA,8OAAC;4DAAE,MAAK;4DAAY,WAAU;;8EAC1B,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;8EAAK;;;;;;;;;;;;;;;;;kEAGd,8OAAC,mIAAA,CAAA,oBAAiB;wDAAC,OAAO;kEACtB,cAAA,8OAAC;4DAAE,MAAK;4DAAa,WAAU;;8EAC3B,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ1B,8OAAC,mIAAA,CAAA,kBAAe;;0DACZ,8OAAC,mIAAA,CAAA,oBAAiB;gDAAC,SAAS,IAAM,kBAAkB,CAAC;gDAAiB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAC9E,+BACA,kBAAkB;;kEAElB,8OAAC;wDAAI,WAAU;;0EACX,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;gEAAO,OAAO;;;;;;0EAC/B,8OAAC;0EAAK;;;;;;;;;;;;oDAET,+BAAiB,8OAAC,gNAAA,CAAA,YAAS;;;;6EAAM,8OAAC,oNAAA,CAAA,cAAW;;;;;;;;;;;4CAEjD,gCACG,8OAAC;gDAAI,WAAU;;kEACX,8OAAC,mIAAA,CAAA,oBAAiB;wDAAC,OAAO;kEACtB,cAAA,8OAAC;4DAAE,MAAK;4DAAkB,WAAU;;8EAChC,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;8EAAK;;;;;;;;;;;;;;;;;kEAGd,8OAAC,mIAAA,CAAA,oBAAiB;wDAAC,OAAO;kEACtB,cAAA,8OAAC;4DAAE,MAAK;4DAAmB,WAAU;;8EACjC,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ1B,8OAAC,mIAAA,CAAA,kBAAe;;0DACZ,8OAAC,mIAAA,CAAA,oBAAiB;gDAAC,SAAS,IAAM,gBAAgB,CAAC;gDAAe,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAC1E,+BACA,gBAAgB;;kEAEhB,8OAAC;wDAAI,WAAU;;0EACX,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;gEAAO,OAAO;;;;;;0EAClC,8OAAC;0EAAK;;;;;;;;;;;;oDAET,6BAAe,8OAAC,gNAAA,CAAA,YAAS;;;;6EAAM,8OAAC,oNAAA,CAAA,cAAW;;;;;;;;;;;4CAE/C,8BACG,8OAAC;gDAAI,WAAU;0DACX,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;oDAAC,OAAO;8DACtB,cAAA,8OAAC;wDAAE,MAAK;wDAAkB,WAAU;;0EAChC,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ1B,8OAAC,mIAAA,CAAA,kBAAe;;0DACZ,8OAAC,mIAAA,CAAA,oBAAiB;gDAAC,SAAS,IAAM,iBAAiB,CAAC;gDAAgB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAC5E,+BACA,iBAAiB;;kEAEjB,8OAAC;wDAAI,WAAU;;0EACX,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;gEAAO,OAAO;;;;;;0EAClC,8OAAC;0EAAK;;;;;;;;;;;;oDAET,8BAAgB,8OAAC,gNAAA,CAAA,YAAS;;;;6EAAM,8OAAC,oNAAA,CAAA,cAAW;;;;;;;;;;;4CAEhD,+BACG,8OAAC;gDAAI,WAAU;;kEACX,8OAAC,mIAAA,CAAA,oBAAiB;wDAAC,OAAO;kEACtB,cAAA,8OAAC;4DAAE,MAAK;4DAAiB,WAAU;;8EAC/B,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;8EAAK;;;;;;;;;;;;;;;;;kEAGd,8OAAC,mIAAA,CAAA,oBAAiB;wDAAC,OAAO;kEACtB,cAAA,8OAAC;4DAAE,MAAK;4DAAkB,WAAU;;8EAChC,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW1C,8OAAC,mIAAA,CAAA,gBAAa;0BACV,cAAA,8OAAC,mIAAA,CAAA,cAAW;8BACR,cAAA,8OAAC,mIAAA,CAAA,kBAAe;kCACZ,cAAA,8OAAC,2IAAA,CAAA,cAAW;4BAAC,MAAK;4BAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzD"}}, {"offset": {"line": 2635, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2640, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/src/app/geistsans_9fc57718.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"className\": \"geistsans_9fc57718-module__5N2VMq__className\",\n  \"variable\": \"geistsans_9fc57718-module__5N2VMq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 2644, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2649, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/src/app/geistsans_9fc57718.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"className\": \"geistsans_9fc57718-module__5N2VMq__className\",\n  \"variable\": \"geistsans_9fc57718-module__5N2VMq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 2653, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2659, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/app/geistsans_9fc57718.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/local/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22%22,%22arguments%22:[{%22src%22:%22./fonts/GeistVF.woff%22,%22variable%22:%22--font-geist-sans%22,%22weight%22:%22100%20900%22}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'geistSans', 'geistSans Fallback'\",\n        \n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,+IAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;IAEhB;AACJ;AAEA,IAAI,+IAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,+IAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe"}}, {"offset": {"line": 2674, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2679, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/src/app/geistmono_b9f59162.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"className\": \"geistmono_b9f59162-module__Or8qSa__className\",\n  \"variable\": \"geistmono_b9f59162-module__Or8qSa__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 2683, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2688, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/src/app/geistmono_b9f59162.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"className\": \"geistmono_b9f59162-module__Or8qSa__className\",\n  \"variable\": \"geistmono_b9f59162-module__Or8qSa__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 2692, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2698, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/app/geistmono_b9f59162.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/local/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22%22,%22arguments%22:[{%22src%22:%22./fonts/GeistMonoVF.woff%22,%22variable%22:%22--font-geist-mono%22,%22weight%22:%22100%20900%22}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'geistMono', 'geistMono Fallback'\",\n        \n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,+IAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;IAEhB;AACJ;AAEA,IAAI,+IAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,+IAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe"}}, {"offset": {"line": 2713, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2719, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/hooks/use-toast.ts"], "sourcesContent": ["\"use client\"\n\n// Inspired by react-hot-toast library\nimport * as React from \"react\"\n\nimport type {\n  ToastActionElement,\n  ToastProps,\n} from \"@/components/ui/toast\"\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: \"ADD_TOAST\",\n  UPDATE_TOAST: \"UPDATE_TOAST\",\n  DISMISS_TOAST: \"DISMISS_TOAST\",\n  REMOVE_TOAST: \"REMOVE_TOAST\",\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType[\"ADD_TOAST\"]\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType[\"UPDATE_TOAST\"]\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType[\"DISMISS_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n  | {\n      type: ActionType[\"REMOVE_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: \"REMOVE_TOAST\",\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case \"ADD_TOAST\":\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case \"UPDATE_TOAST\":\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      }\n\n    case \"DISMISS_TOAST\": {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case \"REMOVE_TOAST\":\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach((listener) => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, \"id\">\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: \"UPDATE_TOAST\",\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\n\n  dispatch({\n    type: \"ADD_TOAST\",\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\n  }\n}\n\nexport { useToast, toast }\n"], "names": [], "mappings": ";;;;;AAEA,sCAAsC;AACtC;AAHA;;AAUA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAS3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAc;IACpC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAA2C,EAAE;AAEnD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS,MAAM,EAAE,GAAG,OAAc;IAChC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,sMAAM,QAAQ,CAAQ;IAEhD,sMAAM,SAAS,CAAC;QACd,UAAU,IAAI,CAAC;QACf,OAAO;YACL,MAAM,QAAQ,UAAU,OAAO,CAAC;YAChC,IAAI,QAAQ,CAAC,GAAG;gBACd,UAAU,MAAM,CAAC,OAAO;YAC1B;QACF;IACF,GAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAC3E;AACF"}}, {"offset": {"line": 2869, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2875, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/ui/toast.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ToastProvider = ToastPrimitives.Provider\n\nconst ToastViewport = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Viewport\n    ref={ref}\n    className={cn(\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\n      className\n    )}\n    {...props}\n  />\n))\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\n\nconst toastVariants = cva(\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\n  {\n    variants: {\n      variant: {\n        default: \"border bg-background text-foreground\",\n        destructive:\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Toast = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\n    VariantProps<typeof toastVariants>\n>(({ className, variant, ...props }, ref) => {\n  return (\n    <ToastPrimitives.Root\n      ref={ref}\n      className={cn(toastVariants({ variant }), className)}\n      {...props}\n    />\n  )\n})\nToast.displayName = ToastPrimitives.Root.displayName\n\nconst ToastAction = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Action>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Action\n    ref={ref}\n    className={cn(\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\n      className\n    )}\n    {...props}\n  />\n))\nToastAction.displayName = ToastPrimitives.Action.displayName\n\nconst ToastClose = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Close>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Close\n    ref={ref}\n    className={cn(\n      \"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\n      className\n    )}\n    toast-close=\"\"\n    {...props}\n  >\n    <X className=\"h-4 w-4\" />\n  </ToastPrimitives.Close>\n))\nToastClose.displayName = ToastPrimitives.Close.displayName\n\nconst ToastTitle = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Title>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Title\n    ref={ref}\n    className={cn(\"text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nToastTitle.displayName = ToastPrimitives.Title.displayName\n\nconst ToastDescription = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Description>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Description\n    ref={ref}\n    className={cn(\"text-sm opacity-90\", className)}\n    {...props}\n  />\n))\nToastDescription.displayName = ToastPrimitives.Description.displayName\n\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\n\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\n\nexport {\n  type ToastProps,\n  type ToastActionElement,\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastClose,\n  ToastAction,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AAEA;AAGA;AAJA;AAEA;AALA;;;;;;;AASA,MAAM,gBAAgB,kKAAgB,QAAQ;AAE9C,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAgB,QAAQ;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAgB,QAAQ,CAAC,WAAW;AAEhE,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6lBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,sMAAM,UAAU,CAI5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACnC,qBACE,8OAAC,kKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AACA,MAAM,WAAW,GAAG,kKAAgB,IAAI,CAAC,WAAW;AAEpD,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAgB,MAAM;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sgBACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAgB,MAAM,CAAC,WAAW;AAE5D,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yVACA;QAEF,eAAY;QACX,GAAG,KAAK;kBAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;YAAC,WAAU;;;;;;;;;;;AAGjB,WAAW,WAAW,GAAG,kKAAgB,KAAK,CAAC,WAAW;AAE1D,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG,kKAAgB,KAAK,CAAC,WAAW;AAE1D,MAAM,iCAAmB,sMAAM,UAAU,CAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAgB,WAAW;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,kKAAgB,WAAW,CAAC,WAAW"}}, {"offset": {"line": 2982, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2988, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/ui/toaster.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useToast } from \"@/hooks/use-toast\"\nimport {\n  Toast,\n  ToastClose,\n  ToastDescription,\n  ToastProvider,\n  ToastTitle,\n  ToastViewport,\n} from \"@/components/ui/toast\"\n\nexport function Toaster() {\n  const { toasts } = useToast()\n\n  return (\n    <ToastProvider>\n      {toasts.map(function ({ id, title, description, action, ...props }) {\n        return (\n          <Toast key={id} {...props}>\n            <div className=\"grid gap-1\">\n              {title && <ToastTitle>{title}</ToastTitle>}\n              {description && (\n                <ToastDescription>{description}</ToastDescription>\n              )}\n            </div>\n            {action}\n            <ToastClose />\n          </Toast>\n        )\n      })}\n      <ToastViewport />\n    </ToastProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAE1B,qBACE,8OAAC,iIAAA,CAAA,gBAAa;;YACX,OAAO,GAAG,CAAC,SAAU,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,OAAO;gBAChE,qBACE,8OAAC,iIAAA,CAAA,QAAK;oBAAW,GAAG,KAAK;;sCACvB,8OAAC;4BAAI,WAAU;;gCACZ,uBAAS,8OAAC,iIAAA,CAAA,aAAU;8CAAE;;;;;;gCACtB,6BACC,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAGtB;sCACD,8OAAC,iIAAA,CAAA,aAAU;;;;;;mBARD;;;;;YAWhB;0BACA,8OAAC,iIAAA,CAAA,gBAAa;;;;;;;;;;;AAGpB"}}, {"offset": {"line": 3054, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3156, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\n\n/**\n * Serviço de API que funciona com Auth0\n * Recebe uma função para obter tokens quando necessário\n */\nclass ApiService {\n  private api: AxiosInstance;\n  private getTokenFn: (() => Promise<string | null>) | null = null;\n\n  constructor() {\n    this.api = axios.create({\n      baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  /**\n   * Configura a função para obter tokens\n   */\n  setTokenProvider(getTokenFn: () => Promise<string | null>) {\n    this.getTokenFn = getTokenFn;\n  }\n\n  /**\n   * Obtém o token usando a função configurada\n   */\n  private async getToken(): Promise<string | null> {\n    if (!this.getTokenFn) {\n      console.warn('⚠️ [API] Token provider não configurado');\n      return null;\n    }\n    return await this.getTokenFn();\n  }\n\n  /**\n   * Configura interceptors para tratar erros\n   */\n  private setupInterceptors() {\n    // Response interceptor - trata erros de autenticação\n    this.api.interceptors.response.use(\n      (response) => response,\n      (error) => {\n        if (error.response?.status === 401) {\n          console.log('🔄 [API] Token expirado (401) - limpando dados e redirecionando...');\n          \n          // Limpar dados locais\n          if (typeof window !== 'undefined') {\n            localStorage.removeItem('user_data');\n            localStorage.removeItem('@@auth0spajs@@::' + process.env.NEXT_PUBLIC_AUTH0_CLIENT_ID);\n            \n            // Redirecionar para login\n            window.location.href = '/login';\n          }\n        }\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  /**\n   * Método auxiliar para fazer requisições com autenticação\n   */\n  private async makeAuthenticatedRequest<T = any>(\n    config: AxiosRequestConfig\n  ): Promise<AxiosResponse<T>> {\n    const token = await this.getToken();\n    \n    console.log('🔍 [API] Fazendo requisição autenticada:', { \n      url: config.url, \n      method: config.method,\n      token: token ? 'Encontrado' : 'Não encontrado'\n    });\n    \n    if (!token) {\n      throw new Error('Token de autenticação não encontrado - verifique se está logado');\n    }\n    \n    const requestConfig = {\n      ...config,\n      headers: {\n        ...config.headers,\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    };\n    \n    console.log('🔍 [API] Config da requisição:', {\n      ...requestConfig,\n      headers: {\n        ...requestConfig.headers,\n        'Authorization': `Bearer ${token.substring(0, 20)}...`\n      }\n    });\n\n    try {\n      const response = await this.api.request<T>(requestConfig);\n      console.log('✅ [API] Resposta recebida:', {\n        status: response.status,\n        statusText: response.statusText,\n        dataType: typeof response.data,\n        dataLength: Array.isArray(response.data) ? response.data.length : 'N/A'\n      });\n      return response;\n    } catch (error) {\n      console.error('❌ [API] Erro na requisição:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Verificar autenticação do usuário\n   */\n  async verifyAuth(): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'GET',\n      url: '/api/auth/verify',\n    });\n  }\n\n  /**\n   * Obter lista de agendamentos\n   */\n  async getSchedules(): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'GET',\n      url: '/api/schedules',\n    });\n  }\n\n  /**\n   * Obter lista de empresas\n   */\n  async getCompanies(): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'GET',\n      url: '/api/empresas',\n    });\n  }\n\n  /**\n   * Atualizar empresa\n   */\n  async updateCompany(id: string, data: any): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'PUT',\n      url: `/api/empresas/${id}`,\n      data,\n    });\n  }\n\n  /**\n   * Deletar empresa\n   */\n  async deleteCompany(id: string): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'DELETE',\n      url: `/api/empresas/${id}`,\n    });\n  }\n\n  /**\n   * Obter lista de servidores\n   */\n  async getServers(): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'GET',\n      url: '/api/server/configs',\n    });\n  }\n\n  /**\n   * Obter lista de configurações SMTP\n   */\n  async getSMTPConfigs(): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'GET',\n      url: '/api/smtp/config/list',\n    });\n  }\n\n  /**\n   * Obter lista de destinatários\n   */\n  async getRecipients(): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'GET',\n      url: '/api/destinatarios/list',\n    });\n  }\n\n  /**\n   * Obter lista de servidores NPrinting\n   */\n  async getNPrintingServers(): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'GET',\n      url: '/api/nprinting/server/list',\n    });\n  }\n\n  /**\n   * Obter lista de servidores Qlik Cloud\n   */\n  async getQlikCloudServers(): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'GET',\n      url: '/api/qlikcloud/server/configs',\n    });\n  }\n\n  /**\n   * Executar agendamento manualmente\n   */\n  async executeSchedule(id: string): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'POST',\n      url: `/api/schedules/${id}/execute`,\n    });\n  }\n\n  /**\n   * Deletar agendamento\n   */\n  async deleteSchedule(id: string): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'DELETE',\n      url: `/api/schedules/${id}`,\n    });\n  }\n\n  /**\n   * Atualizar agendamento\n   */\n  async updateSchedule(id: string, data: any): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'PUT',\n      url: `/api/schedules/${id}`,\n      data,\n    });\n  }\n\n  /**\n   * Criar novo agendamento\n   */\n  async createSchedule(data: any): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'POST',\n      url: '/api/schedules',\n      data,\n    });\n  }\n\n  /**\n   * Obter histórico de execução de um agendamento\n   */\n  async getScheduleHistory(id: string): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'GET',\n      url: `/api/schedules/${id}/history`,\n    });\n  }\n\n  /**\n   * Obter logs em tempo real de um agendamento\n   */\n  async getScheduleLogs(id: string): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'GET',\n      url: `/api/schedules/${id}/logs`,\n    });\n  }\n\n  /**\n   * Obter status de execução de um agendamento\n   */\n  async getScheduleExecutionStatus(id: string): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'GET',\n      url: `/api/schedules/${id}/executing`,\n    });\n  }\n\n  /**\n   * Obter tasks NPrinting de um servidor\n   */\n  async getNPrintingTasks(serverId: string): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'POST',\n      url: `/api/nprinting/tasks`,\n      data: { nprintingServerId: serverId },\n    });\n  }\n\n  /**\n   * Obter um servidor NPrinting específico\n   */\n  async getNPrintingServer(id: string): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'GET',\n      url: `/api/nprinting/server/get/${id}`,\n    });\n  }\n\n  /**\n   * Obter aplicativos Qlik de um servidor\n   */\n  async getQlikApps(serverId: string): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'GET',\n      url: `/api/qlik/server/${serverId}/apps`,\n    });\n  }\n\n  /**\n   * Obter aplicativos Qlik Cloud de um servidor\n   */\n  async getQlikCloudApps(serverId: string): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'GET',\n      url: `/api/qlikcloud/server/${serverId}/apps`,\n    });\n  }\n\n  /**\n   * Obter tasks Qlik de um servidor\n   */\n  async getQlikTasks(serverId: string): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'GET',\n      url: `/api/qlik/server/${serverId}/tasks`,\n    });\n  }\n\n  /**\n   * Obter uma configuração SMTP específica\n   */\n  async getSMTPConfig(id: string): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'GET',\n      url: `/api/smtp/config/get/${id}`,\n    });\n  }\n\n  /**\n   * Obter destinatários por empresa\n   */\n  async getRecipientsByCompany(companyId: string): Promise<AxiosResponse<any>> {\n    return this.makeAuthenticatedRequest({\n      method: 'GET',\n      url: `/api/destinatarios/list/${companyId}`,\n    });\n  }\n\n  /**\n   * Obter destinatários por IDs\n   */\n  async getRecipientsByIds(destinatarios: { value: string; label: string }[] | string[]): Promise<AxiosResponse<any>> {\n    // Verifica se é array de objetos ou array de strings\n    const ids = Array.isArray(destinatarios) && destinatarios.length > 0\n      ? typeof destinatarios[0] === 'string'\n        ? destinatarios as string[]  // Array de strings (IDs diretos)\n        : (destinatarios as { value: string; label: string }[]).map(dest => dest.value) // Array de objetos\n      : [];\n\n    return this.makeAuthenticatedRequest({\n      method: 'POST',\n      url: '/api/destinatarios/list/ids',\n      data: { ids },\n    });\n  }\n\n  /**\n   * Método genérico para fazer requisições\n   */\n  async request<T>(config: AxiosRequestConfig): Promise<AxiosResponse<T>> {\n    return this.makeAuthenticatedRequest<T>(config);\n  }\n}\n\nexport const apiService = new ApiService();\n"], "names": [], "mappings": ";;;AAAA;;AAEA;;;CAGC,GACD,MAAM;IACI,IAAmB;IACnB,aAAoD,KAAK;IAEjE,aAAc;QACZ,IAAI,CAAC,GAAG,GAAG,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YACtB,OAAO;YACP,SAAS;YACT,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,iBAAiB;IACxB;IAEA;;GAEC,GACD,iBAAiB,UAAwC,EAAE;QACzD,IAAI,CAAC,UAAU,GAAG;IACpB;IAEA;;GAEC,GACD,MAAc,WAAmC;QAC/C,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,UAAU;IAC9B;IAEA;;GAEC,GACD,AAAQ,oBAAoB;QAC1B,qDAAqD;QACrD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAChC,CAAC,WAAa,UACd,CAAC;YACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,QAAQ,GAAG,CAAC;gBAEZ,sBAAsB;gBACtB,uCAAmC;;gBAMnC;YACF;YACA,OAAO,QAAQ,MAAM,CAAC;QACxB;IAEJ;IAEA;;GAEC,GACD,MAAc,yBACZ,MAA0B,EACC;QAC3B,MAAM,QAAQ,MAAM,IAAI,CAAC,QAAQ;QAEjC,QAAQ,GAAG,CAAC,4CAA4C;YACtD,KAAK,OAAO,GAAG;YACf,QAAQ,OAAO,MAAM;YACrB,OAAO,QAAQ,eAAe;QAChC;QAEA,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,gBAAgB;YACpB,GAAG,MAAM;YACT,SAAS;gBACP,GAAG,OAAO,OAAO;gBACjB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBAClC,gBAAgB;YAClB;QACF;QAEA,QAAQ,GAAG,CAAC,kCAAkC;YAC5C,GAAG,aAAa;YAChB,SAAS;gBACP,GAAG,cAAc,OAAO;gBACxB,iBAAiB,CAAC,OAAO,EAAE,MAAM,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC;YACxD;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAI;YAC3C,QAAQ,GAAG,CAAC,8BAA8B;gBACxC,QAAQ,SAAS,MAAM;gBACvB,YAAY,SAAS,UAAU;gBAC/B,UAAU,OAAO,SAAS,IAAI;gBAC9B,YAAY,MAAM,OAAO,CAAC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG;YACpE;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,aAA0C;QAC9C,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK;QACP;IACF;IAEA;;GAEC,GACD,MAAM,eAA4C;QAChD,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK;QACP;IACF;IAEA;;GAEC,GACD,MAAM,eAA4C;QAChD,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK;QACP;IACF;IAEA;;GAEC,GACD,MAAM,cAAc,EAAU,EAAE,IAAS,EAA+B;QACtE,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK,CAAC,cAAc,EAAE,IAAI;YAC1B;QACF;IACF;IAEA;;GAEC,GACD,MAAM,cAAc,EAAU,EAA+B;QAC3D,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK,CAAC,cAAc,EAAE,IAAI;QAC5B;IACF;IAEA;;GAEC,GACD,MAAM,aAA0C;QAC9C,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK;QACP;IACF;IAEA;;GAEC,GACD,MAAM,iBAA8C;QAClD,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK;QACP;IACF;IAEA;;GAEC,GACD,MAAM,gBAA6C;QACjD,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK;QACP;IACF;IAEA;;GAEC,GACD,MAAM,sBAAmD;QACvD,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK;QACP;IACF;IAEA;;GAEC,GACD,MAAM,sBAAmD;QACvD,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK;QACP;IACF;IAEA;;GAEC,GACD,MAAM,gBAAgB,EAAU,EAA+B;QAC7D,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK,CAAC,eAAe,EAAE,GAAG,QAAQ,CAAC;QACrC;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,EAAU,EAA+B;QAC5D,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK,CAAC,eAAe,EAAE,IAAI;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,EAAU,EAAE,IAAS,EAA+B;QACvE,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK,CAAC,eAAe,EAAE,IAAI;YAC3B;QACF;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,IAAS,EAA+B;QAC3D,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK;YACL;QACF;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB,EAAU,EAA+B;QAChE,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK,CAAC,eAAe,EAAE,GAAG,QAAQ,CAAC;QACrC;IACF;IAEA;;GAEC,GACD,MAAM,gBAAgB,EAAU,EAA+B;QAC7D,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK,CAAC,eAAe,EAAE,GAAG,KAAK,CAAC;QAClC;IACF;IAEA;;GAEC,GACD,MAAM,2BAA2B,EAAU,EAA+B;QACxE,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK,CAAC,eAAe,EAAE,GAAG,UAAU,CAAC;QACvC;IACF;IAEA;;GAEC,GACD,MAAM,kBAAkB,QAAgB,EAA+B;QACrE,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK,CAAC,oBAAoB,CAAC;YAC3B,MAAM;gBAAE,mBAAmB;YAAS;QACtC;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB,EAAU,EAA+B;QAChE,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK,CAAC,0BAA0B,EAAE,IAAI;QACxC;IACF;IAEA;;GAEC,GACD,MAAM,YAAY,QAAgB,EAA+B;QAC/D,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK,CAAC,iBAAiB,EAAE,SAAS,KAAK,CAAC;QAC1C;IACF;IAEA;;GAEC,GACD,MAAM,iBAAiB,QAAgB,EAA+B;QACpE,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK,CAAC,sBAAsB,EAAE,SAAS,KAAK,CAAC;QAC/C;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,QAAgB,EAA+B;QAChE,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK,CAAC,iBAAiB,EAAE,SAAS,MAAM,CAAC;QAC3C;IACF;IAEA;;GAEC,GACD,MAAM,cAAc,EAAU,EAA+B;QAC3D,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK,CAAC,qBAAqB,EAAE,IAAI;QACnC;IACF;IAEA;;GAEC,GACD,MAAM,uBAAuB,SAAiB,EAA+B;QAC3E,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK,CAAC,wBAAwB,EAAE,WAAW;QAC7C;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB,aAA4D,EAA+B;QAClH,qDAAqD;QACrD,MAAM,MAAM,MAAM,OAAO,CAAC,kBAAkB,cAAc,MAAM,GAAG,IAC/D,OAAO,aAAa,CAAC,EAAE,KAAK,WAC1B,gBACA,AAAC,cAAqD,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK,EAAE,mBAAmB;WACnG,EAAE;QAEN,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACnC,QAAQ;YACR,KAAK;YACL,MAAM;gBAAE;YAAI;QACd;IACF;IAEA;;GAEC,GACD,MAAM,QAAW,MAA0B,EAA6B;QACtE,OAAO,IAAI,CAAC,wBAAwB,CAAI;IAC1C;AACF;AAEO,MAAM,aAAa,IAAI"}}, {"offset": {"line": 3466, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3472, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/contexts/AuthProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { Auth0Provider } from '@auth0/auth0-react';\nimport { ReactNode, useEffect } from 'react';\nimport { apiService } from '@/services/api';\nimport { useAuth } from '@/hooks/useAuth';\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\n/**\n * Componente que configura o token provider para o apiService\n */\nfunction ApiTokenProvider({ children }: { children: ReactNode }) {\n  const { getToken } = useAuth();\n\n  useEffect(() => {\n    // Configurar o token provider do apiService\n    apiService.setTokenProvider(getToken);\n    console.log('✅ [AUTH] Token provider configurado para apiService');\n  }, [getToken]);\n\n  return <>{children}</>;\n}\n\n/**\n * Provider Auth0 que envolve toda a aplicação\n * Configura o Auth0 com as variáveis de ambiente\n */\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const domain = process.env.NEXT_PUBLIC_AUTH0_DOMAIN!;\n  const clientId = process.env.NEXT_PUBLIC_AUTH0_CLIENT_ID!;\n  const audience = process.env.NEXT_PUBLIC_AUTH0_AUDIENCE!;\n  const redirectUri = typeof window !== 'undefined' ? `${window.location.origin}/callback` : '';\n\n  if (!domain || !clientId || !audience) {\n    throw new Error('Variáveis de ambiente Auth0 não configuradas corretamente');\n  }\n\n  return (\n    <Auth0Provider\n      domain={domain}\n      clientId={clientId}\n      authorizationParams={{\n        redirect_uri: redirectUri,\n        audience: audience,\n        scope: 'openid profile email offline_access'\n      }}\n      useRefreshTokens={true}\n      cacheLocation=\"localstorage\"\n    >\n      <ApiTokenProvider>\n        {children}\n      </ApiTokenProvider>\n    </Auth0Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWA;;CAEC,GACD,SAAS,iBAAiB,EAAE,QAAQ,EAA2B;IAC7D,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4CAA4C;QAC5C,sHAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC;QAC5B,QAAQ,GAAG,CAAC;IACd,GAAG;QAAC;KAAS;IAEb,qBAAO;kBAAG;;AACZ;AAMO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM,cAAc,6EAAuE;IAE3F,uCAAuC;;IAEvC;IAEA,qBACE,8OAAC,0KAAA,CAAA,gBAAa;QACZ,QAAQ;QACR,UAAU;QACV,qBAAqB;YACnB,cAAc;YACd,UAAU;YACV,OAAO;QACT;QACA,kBAAkB;QAClB,eAAc;kBAEd,cAAA,8OAAC;sBACE;;;;;;;;;;;AAIT"}}, {"offset": {"line": 3532, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3538, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/app/geistsans_9fc57718.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/local/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22%22,%22arguments%22:[{%22src%22:%22./fonts/GeistVF.woff%22,%22variable%22:%22--font-geist-sans%22,%22weight%22:%22100%20900%22}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'geistSans', 'geistSans Fallback'\",\n        \n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,+IAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;IAEhB;AACJ;AAEA,IAAI,+IAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,+IAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe"}}, {"offset": {"line": 3553, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3559, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/app/geistmono_b9f59162.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/local/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22%22,%22arguments%22:[{%22src%22:%22./fonts/GeistMonoVF.woff%22,%22variable%22:%22--font-geist-mono%22,%22weight%22:%22100%20900%22}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'geistMono', 'geistMono Fallback'\",\n        \n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,+IAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;IAEhB;AACJ;AAEA,IAAI,+IAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,+IAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe"}}, {"offset": {"line": 3574, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3580, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/app/layout.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, SidebarTrigger, useSidebar } from \"@/components/ui/sidebar\"\nimport { AppSidebar } from \"@/components/app-sidebar\"\nimport localFont from \"next/font/local\";\nimport \"./globals.css\";\nimport * as React from \"react\"\nimport { usePathname } from 'next/navigation';  // Importando o hook para obter o pathname da URL\nimport { Toaster } from \"@/components/ui/toaster\"\n\n// Importações Auth0\nimport { AuthProvider } from \"@/contexts/AuthProvider\"\n\nconst geistSans = localFont({\n  src: \"./fonts/GeistVF.woff\",\n  variable: \"--font-geist-sans\",\n  weight: \"100 900\",\n});\nconst geistMono = localFont({\n  src: \"./fonts/GeistMonoVF.woff\",\n  variable: \"--font-geist-mono\",\n  weight: \"100 900\",\n});\n\nexport default function Layout({ children }: { children: React.ReactNode }) {\n  return (\n    <html lang=\"en\">\n      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>\n        {/* AuthProvider envolve toda a aplicação para fornecer contexto Auth0 */}\n        <AuthProvider>\n            <AppContent>{children}</AppContent>\n        </AuthProvider>\n        <Toaster />\n      </body>\n    </html>\n  );\n}\n\n/**\n * Componente que gerencia o conteúdo da aplicação com autenticação\n * Aplica proteção de rotas\n */\nfunction AppContent({ children }: { children: React.ReactNode }) {\n  const pathname = usePathname();\n\n  // Páginas que não precisam de sidebar nem AuthGuard\n  const pagesWithoutSidebar = ['/login', '/callback'];\n  const shouldShowSidebar = !pagesWithoutSidebar.includes(pathname);\n\n  // Se não deve mostrar sidebar, renderizar apenas o conteúdo (sem AuthGuard)\n  if (!shouldShowSidebar) {\n    return <>{children}</>;\n  }\n\n  // Layout com sidebar para outras páginas\n  return (\n    <SidebarProvider>\n      <AppSidebar />\n      <LayoutContent>{children}</LayoutContent>\n    </SidebarProvider>\n  );\n}\n\nfunction LayoutContent({ children }: { children: React.ReactNode }) {\n  const { state } = useSidebar();  // Agora o useSidebar funciona corretamente\n  return (\n    <main\n      className={state === \"expanded\" ? \"main-with-sidebar-expanded\" : \"main-with-sidebar-collapsed\"}  // Controle da largura com base no estado\n    >\n      <SidebarTrigger />\n      {children}\n    </main>\n  );\n}\n\n\n\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAIA,mOAAgD,iDAAiD;AACjG;AAEA,oBAAoB;AACpB;AAXA;;;;;;;;;;AAwBe,SAAS,OAAO,EAAE,QAAQ,EAAiC;IACxE,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YAAK,WAAW,GAAG,gIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,gIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;;8BAExE,8OAAC,gIAAA,CAAA,eAAY;8BACT,cAAA,8OAAC;kCAAY;;;;;;;;;;;8BAEjB,8OAAC,mIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;AAIhB;AAEA;;;CAGC,GACD,SAAS,WAAW,EAAE,QAAQ,EAAiC;IAC7D,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,oDAAoD;IACpD,MAAM,sBAAsB;QAAC;QAAU;KAAY;IACnD,MAAM,oBAAoB,CAAC,oBAAoB,QAAQ,CAAC;IAExD,4EAA4E;IAC5E,IAAI,CAAC,mBAAmB;QACtB,qBAAO;sBAAG;;IACZ;IAEA,yCAAyC;IACzC,qBACE,8OAAC,mIAAA,CAAA,kBAAe;;0BACd,8OAAC,oIAAA,CAAA,aAAU;;;;;0BACX,8OAAC;0BAAe;;;;;;;;;;;;AAGtB;AAEA,SAAS,cAAc,EAAE,QAAQ,EAAiC;IAChE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,KAAM,2CAA2C;IAC5E,qBACE,8OAAC;QACC,WAAW,UAAU,aAAa,+BAA+B;;0BAEjE,8OAAC,mIAAA,CAAA,iBAAc;;;;;YACd;;;;;;;AAGP"}}, {"offset": {"line": 3695, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}