{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,sMAAM,UAAU,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,sMAAM,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,sMAAM,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG"}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE"}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Switch = React.forwardRef<\r\n  React.ElementRef<typeof SwitchPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      \"peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  >\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\r\n      )}\r\n    />\r\n  </SwitchPrimitives.Root>\r\n));\r\nSwitch.displayName = SwitchPrimitives.Root.displayName;\r\n\r\nexport { Switch }; "], "names": [], "mappings": ";;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,uBAAS,sMAAM,UAAU,CAG7B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAiB,IAAI;QACpB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+XACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,8OAAC,mKAAiB,KAAK;YACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,mKAAiB,IAAI,CAAC,WAAW"}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Table = React.forwardRef<\n  HTMLTableElement,\n  React.HTMLAttributes<HTMLTableElement>\n>(({ className, ...props }, ref) => (\n  <div className=\"relative w-full overflow-auto\">\n    <table\n      ref={ref}\n      className={cn(\"w-full caption-bottom text-sm\", className)}\n      {...props}\n    />\n  </div>\n))\nTable.displayName = \"Table\"\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\n))\nTableHeader.displayName = \"TableHeader\"\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody\n    ref={ref}\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\n    {...props}\n  />\n))\nTableBody.displayName = \"TableBody\"\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn(\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableFooter.displayName = \"TableFooter\"\n\nconst TableRow = React.forwardRef<\n  HTMLTableRowElement,\n  React.HTMLAttributes<HTMLTableRowElement>\n>(({ className, ...props }, ref) => (\n  <tr\n    ref={ref}\n    className={cn(\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nTableRow.displayName = \"TableRow\"\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableHead.displayName = \"TableHead\"\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\n    {...props}\n  />\n))\nTableCell.displayName = \"TableCell\"\n\nconst TableCaption = React.forwardRef<\n  HTMLTableCaptionElement,\n  React.HTMLAttributes<HTMLTableCaptionElement>\n>(({ className, ...props }, ref) => (\n  <caption\n    ref={ref}\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nTableCaption.displayName = \"TableCaption\"\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,sMAAM,UAAU,CAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,sMAAM,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,sMAAM,UAAU,CAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,sMAAM,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oGACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,sMAAM,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAC/D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,sMAAM,UAAU,CAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG"}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAJA;;;;;;AAQA,MAAM,SAAS,mKAAgB,IAAI;AAEnC,MAAM,gBAAgB,mKAAgB,OAAO;AAE7C,MAAM,eAAe,mKAAgB,MAAM;AAE3C,MAAM,cAAc,mKAAgB,KAAK;AAEzC,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,mKAAgB,OAAO;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,mKAAgB,KAAK;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,kCAAoB,sMAAM,UAAU,CAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,WAAW;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,mKAAgB,WAAW,CAAC,WAAW"}}, {"offset": {"line": 402, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nconst AlertDialog = AlertDialogPrimitive.Root\n\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\n\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\n\nconst AlertDialogOverlay = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\n\nconst AlertDialogContent = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPortal>\n    <AlertDialogOverlay />\n    <AlertDialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    />\n  </AlertDialogPortal>\n))\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\n\nconst AlertDialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\n\nconst AlertDialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\n\nconst AlertDialogTitle = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold\", className)}\n    {...props}\n  />\n))\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\n\nconst AlertDialogDescription = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nAlertDialogDescription.displayName =\n  AlertDialogPrimitive.Description.displayName\n\nconst AlertDialogAction = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Action\n    ref={ref}\n    className={cn(buttonVariants(), className)}\n    {...props}\n  />\n))\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\n\nconst AlertDialogCancel = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Cancel\n    ref={ref}\n    className={cn(\n      buttonVariants({ variant: \"outline\" }),\n      \"mt-2 sm:mt-0\",\n      className\n    )}\n    {...props}\n  />\n))\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\n\nexport {\n  AlertDialog,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogTrigger,\n  AlertDialogContent,\n  AlertDialogHeader,\n  AlertDialogFooter,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  AlertDialogAction,\n  AlertDialogCancel,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AAGA;AACA;AAHA;AAHA;;;;;;AAQA,MAAM,cAAc,4KAAqB,IAAI;AAE7C,MAAM,qBAAqB,4KAAqB,OAAO;AAEvD,MAAM,oBAAoB,4KAAqB,MAAM;AAErD,MAAM,mCAAqB,sMAAM,UAAU,CAGzC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAqB,OAAO;QAC3B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAGT,mBAAmB,WAAW,GAAG,4KAAqB,OAAO,CAAC,WAAW;AAEzE,MAAM,mCAAqB,sMAAM,UAAU,CAGzC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,4KAAqB,OAAO;gBAC3B,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,4KAAqB,OAAO,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,sMAAM,UAAU,CAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAqB,KAAK;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,4KAAqB,KAAK,CAAC,WAAW;AAErE,MAAM,uCAAyB,sMAAM,UAAU,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAqB,WAAW;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,4KAAqB,WAAW,CAAC,WAAW;AAE9C,MAAM,kCAAoB,sMAAM,UAAU,CAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAqB,MAAM;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,4KAAqB,MAAM,CAAC,WAAW;AAEvE,MAAM,kCAAoB,sMAAM,UAAU,CAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAqB,MAAM;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,4KAAqB,MAAM,CAAC,WAAW"}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAAA;AAAA;AAJA;;;;;;AAQA,MAAM,SAAS,mKAAgB,IAAI;AAEnC,MAAM,cAAc,mKAAgB,KAAK;AAEzC,MAAM,cAAc,mKAAgB,KAAK;AAEzC,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,mKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0TACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,mKAAgB,IAAI;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,qCAAuB,sMAAM,UAAU,CAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,cAAc;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,mKAAgB,cAAc,CAAC,WAAW;AAE7E,MAAM,uCAAyB,sMAAM,UAAU,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,gBAAgB;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,mKAAgB,gBAAgB,CAAC,WAAW;AAE9C,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,mKAAgB,MAAM;kBACrB,cAAA,8OAAC,mKAAgB,OAAO;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ggBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,mKAAgB,QAAQ;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,mKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,mKAAgB,aAAa;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,mKAAgB,QAAQ;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,mKAAgB,IAAI,CAAC,WAAW;AAEzD,MAAM,gCAAkB,sMAAM,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,SAAS;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,mKAAgB,SAAS,CAAC,WAAW"}}, {"offset": {"line": 721, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 727, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AAHA;AAHA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,sMAAM,UAAU,CAI5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAe,IAAI;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,kKAAe,IAAI,CAAC,WAAW"}}, {"offset": {"line": 753, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 759, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/ui/loading-spinner.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { cn } from \"@/lib/utils\"; // Caso você esteja usando algum utilitário para concatenar classes como 'cn'\r\n\r\ninterface LoadingSpinnerProps {\r\n  className?: string;\r\n}\r\n\r\nexport const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ className }) => {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      width=\"24\"\r\n      height=\"24\"\r\n      viewBox=\"0 0 24 24\"\r\n      fill=\"none\"\r\n      stroke=\"currentColor\"\r\n      strokeWidth=\"2\"\r\n      strokeLinecap=\"round\"\r\n      strokeLinejoin=\"round\"\r\n      className={cn(\"animate-spin\", className)}\r\n    >\r\n      <path d=\"M21 12a9 9 0 1 1-6.219-8.56\" />\r\n    </svg>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AACA,qMAAkC,6EAA6E;;;AAMxG,MAAM,iBAAgD,CAAC,EAAE,SAAS,EAAE;IACzE,qBACE,8OAAC;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;QACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;kBAE9B,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAGd"}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/components/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode, useEffect } from 'react';\nimport { useAuth } from '../hooks/useAuth';\nimport { useRouter } from 'next/navigation';\nimport { LoadingSpinner } from '@/components/ui/loading-spinner';\n\ninterface ProtectedRouteProps {\n  children: ReactNode;\n}\n\n/**\n * Componente simplificado que protege rotas\n * Redireciona para /login se não estiver autenticado ou não tiver role Admin\n */\nexport function ProtectedRoute({ children }: ProtectedRouteProps) {\n  const { isLoading, canAccess, needsLogin } = useAuth();\n  const router = useRouter();\n\n  // Redirecionar para login se necessário\n  useEffect(() => {\n    if (needsLogin) {\n      router.push('/login');\n    }\n  }, [needsLogin, router]);\n\n  // Mostrar loading enquanto inicializa\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <LoadingSpinner />\n      </div>\n    );\n  }\n\n  // Se não pode acessar, redirecionar para home apenas se não estiver já na home\n  if (!canAccess) {\n    if (typeof window !== 'undefined' && window.location.pathname !== '/') {\n      router.push('/');\n    }\n    return null;\n  }\n\n  // Usuário autenticado e com role Admin - renderizar conteúdo\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAeO,SAAS,eAAe,EAAE,QAAQ,EAAuB;IAC9D,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;YACd,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAY;KAAO;IAEvB,sCAAsC;IACtC,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,8IAAA,CAAA,iBAAc;;;;;;;;;;IAGrB;IAEA,+EAA+E;IAC/E,IAAI,CAAC,WAAW;QACd,uCAAuE;;QAEvE;QACA,OAAO;IACT;IAEA,6DAA6D;IAC7D,qBAAO;kBAAG;;AACZ"}}, {"offset": {"line": 850, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Documentos/SOEVA/AUTOMATION_REPORT/frontend_AUTOMATION-MANAGER/frontend/src/app/schedules/list/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, use<PERSON>allback } from \"react\";\nimport { use<PERSON><PERSON>, Controller } from \"react-hook-form\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { Clock, History, Loader2, Play, Trash2, Terminal, Info, Edit, PlusCircle } from \"lucide-react\";\nimport {\n    Table,\n    TableBody,\n    TableCell,\n    TableHead,\n    TableHeader,\n    TableRow,\n} from \"@/components/ui/table\";\nimport {\n    Dialog,\n    DialogContent,\n    DialogDescription,\n    DialogHeader,\n    DialogTitle,\n    DialogFooter,\n} from \"@/components/ui/dialog\";\nimport {\n    AlertDialog,\n    AlertDialogAction,\n    AlertDialogCancel,\n    AlertDialogContent,\n    AlertDialogDescription,\n    AlertDialogFooter,\n    <PERSON><PERSON><PERSON><PERSON>ogHeader,\n    <PERSON>ert<PERSON><PERSON>og<PERSON>it<PERSON>,\n} from \"@/components/ui/alert-dialog\";\nimport {\n    Select,\n    SelectContent,\n    SelectItem,\n    SelectTrigger,\n    SelectValue,\n} from \"@/components/ui/select\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\";\nimport { Checkbox } from \"@/components/ui/checkbox\";\nimport AsyncSelect from \"react-select/async\";\nimport { apiService } from \"@/services/api\";\nimport { ProtectedRoute } from \"@/components/ProtectedRoute\";\n\ninterface ServerInfo {\n    _id: string;\n    name: string;\n    serverUrl?: string; // opcional\n}\n\ninterface Schedule {\n    _id: string;\n    name: string;\n    serverOriginType: string;\n    serverOrigin: ServerInfo;\n    serverDestination: ServerInfo;\n    appDownloadId: string;\n    appDownloadName: string;\n    appReplaceId: string;\n    appReplaceName: string;\n    taskId: string;\n    taskName: string;\n    nprintingServerId: ServerInfo;\n    nprintingTaskId: string;\n    nprintingTaskName: string;\n    smtpConfigId: string;\n    destinatarios: string[]; // Array de IDs dos destinatários\n    scheduleType: string;\n    scheduleTime?: string;\n    customSchedule?: { weekDays: number[]; time: string }[];\n    lastExecution?: Date;\n    nextExecution?: Date;\n    isActive: boolean;\n    isExecuting: boolean;\n    executionStartTime?: Date;\n    executionHistory?: {\n        startTime: Date;\n        endTime: Date;\n        success: boolean;\n        steps: {\n            step: number;\n            status: string;\n            message: string;\n        }[];\n        error?: {\n            message: string;\n            stack: string;\n        };\n    }[];\n}\n\n// Tipo específico para o formulário\ninterface ScheduleFormData extends Omit<Schedule, 'destinatarios'> {\n    destinatarios: SelectOption[]; // Array de objetos para o formulário\n}\n\ninterface ExecutionStatus {\n    isExecuting: boolean;\n    executionStartTime: Date | null;\n}\n\ninterface ProgressStatus {\n    download: number;\n    upload: number;\n    replace: number;\n    task: number;\n}\n\ninterface LogEntry {\n    timestamp: Date;\n    message: string;\n    progress?: number;\n}\n\ninterface LogResponse {\n    logs: LogEntry[];\n    progressStatus: ProgressStatus;\n}\n\ninterface NPrintingServer {\n    _id: string;\n    name: string;\n    serverUrl: string;\n}\n\ninterface NPrintingTask {\n    id: string;\n    name: string;\n    description: string;\n}\n\ninterface SMTPConfig {\n    _id: string;\n    company: {\n        _id: string;\n        name: string;\n    };\n    from: {\n        name: string;\n        email: string;\n    };\n}\n\ninterface Destinatario {\n    _id: string;\n    name: string;\n    email: string;\n    company: {\n        _id: string;\n        name: string;\n    };\n}\n\ninterface SelectOption {\n    value: string;\n    label: string;\n    stream?: string;\n}\n\nexport default function ScheduleList() {\n    return (\n        <ProtectedRoute>\n            <ScheduleListPage />\n        </ProtectedRoute>\n    );\n}\n\nfunction ScheduleListPage() {\n    const { toast } = useToast();\n    const [loadingStatus, setLoadingStatus] = useState({\n        AppsOrigin: false,\n        AppsDestination: false,\n        Tasks: false,\n        NPrintingTasks: false\n    });\n    const [isLoading, setIsLoading] = useState(false);\n    const { control, handleSubmit, watch, setValue } = useForm<ScheduleFormData>();\n    const [servers, setServers] = useState<{ _id: string; name: string; company: { _id: string; name: string } }[]>([]);\n    const [serversDestination, setServersDestination] = useState<{ _id: string; name: string; company: { _id: string; name: string } }[]>([]);\n    const [schedules, setSchedules] = useState<Schedule[]>([]);\n    const [loading, setLoading] = useState(true);\n    const [selectedSchedule, setSelectedSchedule] = useState<Schedule | null>(null);\n    const [showHistoryDialog, setShowHistoryDialog] = useState(false);\n    const [showDeleteDialog, setShowDeleteDialog] = useState(false);\n    const [showLogsDialog, setShowLogsDialog] = useState(false);\n    const [showDetailsDialog, setShowDetailsDialog] = useState(false);\n    const [executingSchedules, setExecutingSchedules] = useState<Set<string>>(new Set());\n    const [realtimeLogs, setRealtimeLogs] = useState<LogEntry[]>([]);\n    const [logPollingInterval, setLogPollingInterval] = useState<NodeJS.Timeout | null>(null);\n    const [executionStatus, setExecutionStatus] = useState<Record<string, ExecutionStatus>>({});\n    const [progressStatus, setProgressStatus] = useState<ProgressStatus>({\n        download: 0,\n        upload: 0,\n        replace: 0,\n        task: 0\n    });\n    const [checkedSchedules, setCheckedSchedules] = useState<Set<string>>(new Set());\n    const [nprintingServers, setNPrintingServers] = useState<NPrintingServer[]>([]);\n    const [selectedNPrintingServer, setSelectedNPrintingServer] = useState<NPrintingServer | null>(null);\n    const [selectedSMTPConfig, setSelectedSMTPConfig] = useState<SMTPConfig | null>(null);\n    const [selectedDestinatarios, setSelectedDestinatarios] = useState<Destinatario[]>([]);\n    const [showEditDialog, setShowEditDialog] = useState(false);\n    const [editScheduleData, setEditScheduleData] = useState<Schedule | null>(null);\n\n    const [availableDestinatarios, setAvailableDestinatarios] = useState<Destinatario[]>([]);\n    const [selectedWeekDays, setSelectedWeekDays] = useState<number[]>([]);\n    const [customTime, setCustomTime] = useState(\"\");\n    const [appsOrigin, setAppsOrigin] = useState<SelectOption[]>([]);\n    const [appsDestination, setAppsDestination] = useState<SelectOption[]>([]);\n    const [tasks, setTasks] = useState<SelectOption[]>([]);\n    const [nprintingTasks, setNPrintingTasks] = useState<SelectOption[]>([]);\n    const [smtpConfigs, setSmtpConfigs] = useState<SMTPConfig[]>([]);\n    const [scheduleToExecute, setScheduleToExecute] = useState<string | null>(null);\n\n    const serverOrigin = watch(\"serverOrigin\");\n    const serverOriginType = watch(\"serverOriginType\");\n    const serverDestination = watch(\"serverDestination\");\n    const nprintingServerId = watch(\"nprintingServerId\");\n    const [customSchedules, setCustomSchedules] = useState<{ weekDays: number[]; time: string }[]>([]);\n    const scheduleType = watch(\"scheduleType\");\n    const [destinatarios, setDestinatarios] = useState<SelectOption[]>([]);\n    const weekDays = [\n        { day: 'D', label: 'Domingo', value: 0 },\n        { day: 'S', label: 'Segunda', value: 1 },\n        { day: 'T', label: 'Terça', value: 2 },\n        { day: 'Q', label: 'Quarta', value: 3 },\n        { day: 'Q', label: 'Quinta', value: 4 },\n        { day: 'S', label: 'Sexta', value: 5 },\n        { day: 'S', label: 'Sábado', value: 6 }\n    ];\n\n    const [selectedStream, setSelectedStream] = useState<string>(\"Todos\");\n    const [streamOptions, setStreamOptions] = useState<string[]>([]);\n    const [selectedStreamDestino, setSelectedStreamDestino] = useState<string>(\"Todos\");\n    const [streamOptionsDestino, setStreamOptionsDestino] = useState<string[]>([]);\n    const [isLoadingAppsOrigin, setIsLoadingAppsOrigin] = useState(false);\n    const [isLoadingAppsDestino, setIsLoadingAppsDestino] = useState(false);\n\n    const checkLoadingStatus = (newStatus: { AppsOrigin: boolean, AppsDestination: boolean, Tasks: boolean, NPrintingTasks: boolean }) => {\n        const allLoaded = Object.values(newStatus).every(status => status === true);\n        ////console.log(\"🔹 Verificando status de carregamento:\", newStatus);\n        ////console.log(\"🔹 Carregamento atual:\", allLoaded);\n        if (allLoaded) {\n            //setTimeout(() => {\n            //    setIsLoading(false);\n            //   //console.log(\"✅ Todos os dados carregados, modal pode ser exibido.\");\n            //}, 1500); // 🔹 Adiciona um atraso de 1 segundo e meio antes de atualizar o estado\n            setIsLoading(false);\n        }\n    };\n\n    // 🔹 Buscar lista de servidores ao carregar a página\n    useEffect(() => {\n        if (!serverOriginType) return;\n\n        const fetchServers = async () => {\n            try {\n                const response = serverOriginType === \"cloud\"\n                    ? await apiService.getQlikCloudServers()\n                    : await apiService.getServers();\n                console.log(\"🔹 Servidores encontrados:\", response.data);\n                setServers(response.data);\n            } catch (error) {\n                console.error(\"Erro ao buscar servidores:\", error);\n            }\n        };\n\n        fetchServers();\n    }, [serverOriginType]);\n\n    // 🔹 Buscar lista de servidores de destino ao carregar a página\n    useEffect(() => {\n        const fetchServersDestination = async () => {\n            try {\n                const response = await apiService.getServers();\n                setServersDestination(response.data);\n            } catch (error) {\n                console.error(\"Erro ao buscar servidores:\", error);\n            }\n        };\n\n        fetchServersDestination();\n    }, []);\n\n    useEffect(() => {\n        if (!appsOrigin.length) return;\n\n        const uniqueStreams = [\n            \"Todos\",\n            ...Array.from(new Set(appsOrigin.map(app => app.stream)))\n        ];\n\n        setStreamOptions(uniqueStreams.filter((stream): stream is string => stream !== undefined));\n    }, [appsOrigin]);\n\n    // 🔹 Gerar opções únicas de stream para destino\n    useEffect(() => {\n        if (!appsDestination.length) return;\n\n        const uniqueStreams = [\n            \"Todos\",\n            ...Array.from(new Set(appsDestination.map(app => app.stream)))\n        ];\n\n        setStreamOptionsDestino(uniqueStreams.filter((stream): stream is string => stream !== undefined));\n    }, [appsDestination]);\n\n    // 🔹 Buscar aplicativos do servidor de origem\n    useEffect(() => {\n        if (!serverOrigin?._id || !serverOriginType) return;\n\n        const fetchApps = async () => {\n            try {\n                setIsLoadingAppsOrigin(true);\n                const response = serverOriginType === \"cloud\"\n                    ? await apiService.getQlikCloudApps(serverOrigin._id)\n                    : await apiService.getQlikApps(serverOrigin._id);\n                //console.log(\"🔹 Aplicativos encontrados:\", response.data);\n                if (serverOriginType === \"cloud\") {\n                    setAppsOrigin(response.data.map((app: any) => ({\n                        value: app.resourceId,\n                        label: `Nome: ${app.name} - ID: ${app.resourceId}`,\n                        stream: app.stream || \"Sem Stream\"\n                    })));\n                } else {\n                    setAppsOrigin(response.data.map((app: any) => ({\n                        value: app.id,\n                        label: `Nome: ${app.name} - ID: ${app.id}`,\n                        stream: app.stream || \"Sem Stream\"\n                    })));\n                }\n\n            } catch (error) {\n                console.error(\"Erro ao buscar aplicativos:\", error);\n                setAppsOrigin([]);\n            } finally {\n                setLoadingStatus(prev => {\n                    const newStatus = { ...prev, AppsOrigin: true };\n                    ////console.log(\"🔹 Atualizando loadingStatus:\", newStatus);\n                    checkLoadingStatus(newStatus);\n                    return newStatus;\n                });\n                setIsLoadingAppsOrigin(false);\n            }\n        };\n\n        fetchApps();\n    }, [serverOrigin, serverOriginType]);\n\n    // 🔹 Buscar aplicativos do servidor de destino\n    useEffect(() => {\n        if (!serverDestination?._id) return;\n\n        const fetchApps = async () => {\n            try {\n                setIsLoadingAppsDestino(true);\n                const response = await apiService.getQlikApps(serverDestination._id);\n                setAppsDestination(response.data.map((app: any) => ({\n                    value: app.id,\n                    label: `Nome: ${app.name} - ID: ${app.id}`,\n                    stream: app.stream || \"Sem Stream\"\n                })));\n            } catch (error) {\n                console.error(\"Erro ao buscar aplicativos:\", error);\n                setAppsDestination([]);\n            } finally {\n                setLoadingStatus(prev => {\n                    const newStatus = { ...prev, AppsDestination: true };\n                    ////console.log(\"🔹 Atualizando loadingStatus:\", newStatus);\n                    checkLoadingStatus(newStatus);\n                    return newStatus;\n                });\n                setIsLoadingAppsDestino(false);\n            }\n        };\n\n        fetchApps();\n    }, [serverDestination]);\n\n    // 🔹 Buscar tasks do servidor de destino\n    useEffect(() => {\n        if (!serverDestination?._id) return;\n\n        const fetchTasks = async () => {\n            try {\n                const response = await apiService.getQlikTasks(serverDestination._id);\n                setTasks(response.data.map((task: any) => ({\n                    value: task.id,\n                    label: `Nome: ${task.name} - ID: ${task.id}`\n                })));\n            } catch (error) {\n                console.error(\"Erro ao buscar tasks:\", error);\n                setTasks([]);\n            } finally {\n                setLoadingStatus(prev => {\n                    const newStatus = { ...prev, Tasks: true };\n                    ////console.log(\"🔹 Atualizando loadingStatus:\", newStatus);\n                    checkLoadingStatus(newStatus);\n                    return newStatus;\n                });\n            }\n        };\n\n        fetchTasks();\n    }, [serverDestination]);\n\n    // 🔹 Buscar servidores NPrinting\n    useEffect(() => {\n        const fetchNPrintingServers = async () => {\n            try {\n                const response = await apiService.getNPrintingServers();\n                setNPrintingServers(response.data);\n            } catch (error) {\n                console.error(\"Erro ao buscar servidores NPrinting:\", error);\n                toast({\n                    title: \"Erro\",\n                    description: \"Falha ao carregar lista de servidores NPrinting.\",\n                    variant: \"destructive\",\n                });\n            }\n        };\n\n        fetchNPrintingServers();\n    }, []);\n\n    // 🔹 Buscar tasks do servidor NPrinting selecionado\n    useEffect(() => {\n        if (!nprintingServerId?._id) {\n            setNPrintingTasks([]);\n            setValue(\"nprintingTaskId\", \"\");\n            setValue(\"nprintingTaskName\", \"\");\n            return;\n        }\n\n        const fetchNPrintingTasks = async () => {\n            try {\n                const response = await apiService.getNPrintingTasks(nprintingServerId._id);\n                if (response.data.success) {\n                    setNPrintingTasks(response.data.data.map((task: NPrintingTask) => ({\n                        value: task.id,\n                        label: `Nome: ${task.name} - ID: ${task.id}`\n                    })));\n                } else {\n                    throw new Error(response.data.message || \"Erro ao carregar tasks\");\n                }\n            } catch (error) {\n                console.error(\"Erro ao buscar tasks NPrinting:\", error);\n                toast({\n                    title: \"Erro\",\n                    description: \"Falha ao carregar lista de tasks NPrinting.\",\n                    variant: \"destructive\",\n                });\n                setNPrintingTasks([]);\n            } finally {\n                setLoadingStatus(prev => {\n                    const newStatus = { ...prev, NPrintingTasks: true };\n                    ////console.log(\"🔹 Atualizando loadingStatus:\", newStatus);\n                    checkLoadingStatus(newStatus);\n                    return newStatus;\n                });\n            }\n        };\n\n        fetchNPrintingTasks();\n    }, [nprintingServerId]);\n\n    // 🔹 Buscar lista de SMTPs ao carregar a página\n    useEffect(() => {\n        const fetchSmtpConfigs = async () => {\n            try {\n                const response = await apiService.getSMTPConfigs();\n                setSmtpConfigs(response.data);\n            } catch (error) {\n                console.error(\"Erro ao buscar configurações SMTP:\", error);\n                toast({\n                    title: \"Erro\",\n                    description: \"Falha ao carregar lista de configurações SMTP.\",\n                    variant: \"destructive\",\n                });\n            }\n        };\n\n        fetchSmtpConfigs();\n    }, []);\n\n    // 🔹 Buscar destinatários quando a empresa de origem mudar\n    useEffect(() => {\n        const selectedServer = servers.find(s => s._id === serverOrigin?._id);\n        if (!selectedServer?.company?._id) {\n            setDestinatarios([]);\n            setValue(\"destinatarios\", []);\n            return;\n        }\n\n        const fetchDestinatarios = async () => {\n            try {\n                const response = await apiService.getRecipientsByCompany(selectedServer.company._id);\n                if (!response.data.success) {\n                    throw new Error(response.data.message || \"Falha ao carregar destinatários\");\n                }\n\n                const options = response.data.data.map((dest: Destinatario) => ({\n                    value: dest._id,\n                    label: dest.email\n                }));\n                setDestinatarios(options);\n            } catch (error) {\n                console.error(\"Erro ao buscar destinatários:\", error);\n                toast({\n                    title: \"Erro\",\n                    description: \"Falha ao carregar lista de destinatários.\",\n                    variant: \"destructive\",\n                });\n                setDestinatarios([]);\n            }\n        };\n\n        fetchDestinatarios();\n    }, [serverOrigin?._id, servers]);\n\n    // Função para atualizar o progresso mantendo o maior valor\n    const updateProgress = (current: ProgressStatus, newProgress: ProgressStatus): ProgressStatus => {\n        // Se algum valor novo for menor que o atual, mantém o atual\n        return {\n            download: newProgress.download < current.download ? current.download : newProgress.download,\n            upload: newProgress.upload < current.upload ? current.upload : newProgress.upload,\n            replace: newProgress.replace < current.replace ? current.replace : newProgress.replace,\n            task: newProgress.task < current.task ? current.task : newProgress.task\n        };\n    };\n\n    // Função para formatar data\n    const formatDate = (date: Date | undefined) => {\n        if (!date) return \"N/A\";\n        return new Date(date).toLocaleString(\"pt-BR\");\n    };\n\n    // 🔹 Função para adicionar horários personalizados\n    const addCustomSchedules = () => {\n        if (!customTime || selectedWeekDays.length === 0) {\n            toast({\n                title: \"Atenção\",\n                description: \"Selecione pelo menos um dia da semana e um horário\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n\n        const newSchedule = {\n            weekDays: [...selectedWeekDays].sort((a, b) => a - b),\n            time: customTime\n        };\n\n        setCustomSchedules([...customSchedules, newSchedule]);\n        setSelectedWeekDays([]);\n        setCustomTime(\"\");\n    };\n\n    // 🔹 Função para alternar seleção de dia da semana\n    const toggleWeekDay = (dayValue: number) => {\n        setSelectedWeekDays(prev =>\n            prev.includes(dayValue)\n                ? prev.filter(day => day !== dayValue)\n                : [...prev, dayValue]\n        );\n    };\n\n    // Função para formatar tipo de agendamento\n    const formatScheduleType = (type: string) => {\n        const types = {\n            diario: \"Diário\",\n            semanal: \"Semanal\",\n            mensal: \"Mensal\",\n            personalizado: \"Personalizado\"\n        };\n        return types[type as keyof typeof types] || type;\n    };\n\n    // Função para calcular duração da execução\n    const calculateExecutionDuration = (startTime: Date | string | null) => {\n        if (!startTime) return \"0m 0s\";\n\n        const now = new Date();\n        const start = typeof startTime === 'string' ? new Date(startTime) : startTime;\n\n        // Verifica se a data é válida\n        if (isNaN(start.getTime())) {\n            console.error('Data de início inválida:', startTime);\n            return \"0m 0s\";\n        }\n\n        const diffInSeconds = Math.floor((now.getTime() - start.getTime()) / 1000);\n        ////console.log('Diferença em segundos:', diffInSeconds, 'Start time:', start.toISOString());\n\n        // Se for mais de 24 horas, mostra em horas\n        if (diffInSeconds >= 86400) {\n            const hours = Math.floor(diffInSeconds / 3600);\n            return `${hours}h`;\n        }\n\n        // Se for mais de 1 hora, mostra em horas e minutos\n        if (diffInSeconds >= 3600) {\n            const hours = Math.floor(diffInSeconds / 3600);\n            const minutes = Math.floor((diffInSeconds % 3600) / 60);\n            return `${hours}h ${minutes}m`;\n        }\n\n        // Se for menos de 1 hora, mostra em minutos e segundos\n        const minutes = Math.floor(diffInSeconds / 60);\n        const seconds = diffInSeconds % 60;\n\n        if (minutes === 0) {\n            return `${seconds}s`;\n        }\n\n        return `${minutes}m ${seconds}s`;\n    };\n\n    // Função para buscar logs em tempo real\n    const fetchRealtimeLogs = async (scheduleId: string) => {\n        try {\n            const response = await apiService.getScheduleLogs(scheduleId);\n            const data: LogResponse = response.data;\n\n            // Se temos logs, significa que ainda está executando\n            if (data.logs && data.logs.length > 0) {\n                setExecutingSchedules(prev => new Set(prev).add(scheduleId));\n\n                // Usa o executionStartTime existente\n                setExecutionStatus(prev => {\n                    const currentStatus = prev[scheduleId];\n                    return {\n                        ...prev,\n                        [scheduleId]: {\n                            isExecuting: true,\n                            // Mantém o tempo existente, pois o tempo real vem da rota /executing\n                            executionStartTime: currentStatus?.executionStartTime\n                        }\n                    };\n                });\n\n                setRealtimeLogs(prevLogs => {\n                    const newLogs = processLogs([...prevLogs, ...data.logs]);\n                    return newLogs;\n                });\n            }\n\n            if (data.progressStatus) {\n                setProgressStatus(prev => updateProgress(prev, data.progressStatus));\n            }\n        } catch (error) {\n            console.error('Erro ao buscar logs:', error);\n        }\n    };\n\n    // Função para verificar status de execução dos agendamentos\n    const checkExecutionStatus = useCallback(async () => {\n        const newExecutionStatus: Record<string, ExecutionStatus> = {};\n\n        for (const schedule of schedules) {\n            try {\n                const response = await apiService.getScheduleExecutionStatus(schedule._id);\n                const data = response.data;\n\n                // Marca como verificado\n                setCheckedSchedules(prev => new Set([...prev, schedule._id]));\n\n                if (data.isExecuting) {\n                    setExecutingSchedules(prev => new Set(prev).add(schedule._id));\n                    newExecutionStatus[schedule._id] = {\n                        isExecuting: true,\n                        // Sempre usa o startTime do servidor quando disponível\n                        executionStartTime: data.executionStartTime ? new Date(data.executionStartTime) : null\n                    };\n\n                    // Log para debug\n                    if (data.executionStartTime) {\n                        //console.log(`Tempo de início recebido do servidor para ${schedule._id}:`, data.executionStartTime);\n                    }\n                } else {\n                    setExecutingSchedules(prev => {\n                        const newSet = new Set(prev);\n                        newSet.delete(schedule._id);\n                        return newSet;\n                    });\n                    newExecutionStatus[schedule._id] = {\n                        isExecuting: false,\n                        executionStartTime: null\n                    };\n                }\n            } catch (error) {\n                console.error(`Erro ao verificar status de execução para ${schedule._id}:`, error);\n                // Em caso de erro, mantém o status anterior\n                if (executionStatus[schedule._id]) {\n                    newExecutionStatus[schedule._id] = executionStatus[schedule._id];\n                }\n            }\n        }\n\n        setExecutionStatus(newExecutionStatus);\n    }, [schedules, executionStatus]);\n\n    // Função para buscar agendamentos\n    const fetchSchedules = async () => {\n        try {\n            setLoading(true);\n            const response = await apiService.getSchedules();\n            setSchedules(response.data);\n\n            // Verifica o status de execução imediatamente após carregar os agendamentos\n            await checkExecutionStatus();\n        } catch (error) {\n            toast({\n                title: \"Erro\",\n                description: \"Não foi possível carregar os agendamentos\",\n                variant: \"destructive\",\n            });\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    // Função para buscar histórico de execução\n    const fetchExecutionHistory = async (scheduleId: string) => {\n        try {\n            const response = await apiService.getScheduleHistory(scheduleId);\n            return response.data.executionHistory;\n        } catch (error) {\n            toast({\n                title: \"Erro\",\n                description: \"Não foi possível carregar o histórico\",\n                variant: \"destructive\",\n            });\n            return [];\n        }\n    };\n\n    // Função para executar agendamento manualmente\n    const executeSchedule = async (id: string) => {\n        try {\n            // Não definimos mais o executionStartTime aqui, vamos esperar o valor do servidor\n            setExecutingSchedules(prev => new Set(prev).add(id));\n            setExecutionStatus(prev => ({\n                ...prev,\n                [id]: {\n                    isExecuting: true,\n                    executionStartTime: null // Será atualizado quando recebermos o valor do servidor\n                }\n            }));\n\n            const response = await apiService.executeSchedule(id);\n            // A resposta já é tratada pelo apiService\n\n            toast({\n                title: \"Sucesso\",\n                description: \"Agendamento iniciado com sucesso\",\n            });\n\n            // Inicia verificação de status\n            await checkExecutionStatus();\n\n            // Atualiza a lista apenas uma vez após verificar o status\n            await fetchSchedules();\n\n        } catch (error: any) {\n            toast({\n                title: \"Erro\",\n                description: error.message || \"Não foi possível executar o agendamento\",\n                variant: \"destructive\",\n            });\n\n            // Remove o status de execução em caso de erro\n            setExecutingSchedules(prev => {\n                const newSet = new Set(prev);\n                newSet.delete(id);\n                return newSet;\n            });\n            setExecutionStatus(prev => ({\n                ...prev,\n                [id]: {\n                    isExecuting: false,\n                    executionStartTime: null\n                }\n            }));\n        }\n    };\n\n    // Função para deletar agendamento\n    const deleteSchedule = async () => {\n        if (!selectedSchedule) return;\n\n        try {\n            await apiService.deleteSchedule(selectedSchedule._id);\n\n            toast({\n                title: \"Sucesso\",\n                description: \"Agendamento excluído com sucesso\",\n            });\n\n            setShowDeleteDialog(false);\n            fetchSchedules();\n        } catch (error) {\n            toast({\n                title: \"Erro\",\n                description: \"Não foi possível excluir o agendamento\",\n                variant: \"destructive\",\n            });\n        }\n    };\n\n    // Função para abrir o histórico\n    const openHistory = async (schedule: Schedule) => {\n        setSelectedSchedule(schedule);\n        setShowHistoryDialog(true);\n        const history = await fetchExecutionHistory(schedule._id);\n        if (history) {\n            setSelectedSchedule(prev => prev ? { ...prev, executionHistory: history } : null);\n        }\n    };\n\n    const resetForm = () => {\n        setValue(\"serverOriginType\", \"\");\n        setValue(\"serverOrigin\", { _id: \"\", name: \"\" });\n        setValue(\"appDownloadId\", \"\");\n        setValue(\"appDownloadName\", \"\");\n        setAppsOrigin([]);\n        setValue(\"serverDestination\", { _id: \"\", name: \"\" });\n        setValue(\"appReplaceId\", \"\");\n        setValue(\"appReplaceName\", \"\");\n        setAppsDestination([]);\n        setValue(\"taskId\", \"\");\n        setValue(\"taskName\", \"\");\n        setValue(\"nprintingServerId\", { _id: \"\", name: \"\" });\n        setValue(\"nprintingTaskId\", \"\");\n        setValue(\"nprintingTaskName\", \"\");\n        setValue(\"smtpConfigId\", \"\");\n        setValue(\"scheduleType\", \"\");\n    };\n\n    // Função para processar logs antes de exibir\n    const processLogs = (logs: LogEntry[]) => {\n        const processedLogs = new Map<string, LogEntry>();\n\n        logs.forEach(log => {\n            const key = log.message;\n            // Atualiza apenas o timestamp e progresso se a mensagem já existe\n            if (processedLogs.has(key)) {\n                const existingLog = processedLogs.get(key)!;\n                existingLog.timestamp = log.timestamp;\n                if (log.progress !== undefined) {\n                    existingLog.progress = log.progress;\n                }\n            } else {\n                processedLogs.set(key, { ...log });\n            }\n        });\n\n        return Array.from(processedLogs.values())\n            .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());\n    };\n\n    // Função para abrir o dialog de logs\n    const openLogsDialog = (schedule: Schedule) => {\n        setSelectedSchedule(schedule);\n        setShowLogsDialog(true);\n        fetchRealtimeLogs(schedule._id);\n\n        // Inicia o polling dos logs\n        const interval = setInterval(() => {\n            fetchRealtimeLogs(schedule._id);\n        }, 2000); // Atualiza a cada 2 segundos\n\n        setLogPollingInterval(interval);\n    };\n\n    // Função para limpar logs ao fechar o dialog\n    const closeLogsDialog = () => {\n        if (logPollingInterval) {\n            clearInterval(logPollingInterval);\n        }\n        setShowLogsDialog(false);\n        setRealtimeLogs([]);\n        setProgressStatus({\n            download: 0,\n            upload: 0,\n            replace: 0,\n            task: 0\n        });\n    };\n\n    // Efeito para verificar status de execução periodicamente\n    useEffect(() => {\n        const interval = setInterval(checkExecutionStatus, 5000); // Verifica a cada 5 segundos\n        return () => clearInterval(interval);\n    }, [checkExecutionStatus]);\n\n    // Carregar agendamentos ao montar o componente\n    useEffect(() => {\n        fetchSchedules();\n    }, []);\n\n    // 🔹 Buscar servidores NPrinting\n    useEffect(() => {\n        const fetchNPrintingServers = async () => {\n            try {\n                const response = await apiService.getNPrintingServers();\n                setNPrintingServers(response.data);\n            } catch (error) {\n                console.error(\"Erro ao buscar servidores NPrinting:\", error);\n                toast({\n                    title: \"Erro\",\n                    description: \"Falha ao carregar lista de servidores NPrinting.\",\n                    variant: \"destructive\",\n                });\n            }\n        };\n\n        fetchNPrintingServers();\n    }, []);\n\n    // Função para formatar dias da semana\n    const formatWeekDays = (days: number[]) => {\n        const weekDays = [\n            'Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'\n        ];\n        return days.map(day => weekDays[day]).join(', ');\n    };\n\n    // Função para renderizar a barra de progresso\n    const renderProgressBar = (value: number, label: string) => (\n        <div className=\"space-y-1\">\n            <div className=\"flex justify-between text-xs\">\n                <span>{label}</span>\n                <span>{value}%</span>\n            </div>\n            <div className=\"h-2 bg-secondary rounded-full overflow-hidden\">\n                <div\n                    className=\"h-full bg-primary transition-all duration-300 ease-in-out\"\n                    style={{ width: `${value}%` }}\n                />\n            </div>\n        </div>\n    );\n\n    // Função para calcular o progresso geral\n    const calculateOverallProgress = (status: ProgressStatus) => {\n        const values = Object.values(status);\n        return Math.round(values.reduce((acc, val) => acc + val, 0) / values.length);\n    };\n\n    // Função para buscar dados detalhados do agendamento\n    const fetchScheduleDetails = async (schedule: Schedule) => {\n        try {\n            //console.log('Schedule details:', schedule); // Debug\n\n            // Buscar servidor NPrinting\n            if (schedule.nprintingServerId) {\n                //console.log('Buscando servidor NPrinting:', schedule.nprintingServerId._id); // Debug\n                try {\n                    const nprintingResponse = await apiService.getNPrintingServer(schedule.nprintingServerId._id);\n                    //console.log('NPrinting response:', nprintingResponse.data); // Debug\n                    setSelectedNPrintingServer(nprintingResponse.data);\n                } catch (error) {\n                    console.error('Erro ao buscar servidor NPrinting:', error);\n                    toast({\n                        title: \"Aviso\",\n                        description: \"Não foi possível carregar os dados do servidor NPrinting\",\n                        variant: \"destructive\",\n                    });\n                }\n            }\n\n            // Buscar configuração SMTP\n            if (schedule.smtpConfigId) {\n                //console.log('Buscando configuração SMTP:', schedule.smtpConfigId); // Debug\n                try {\n                    const smtpResponse = await apiService.getSMTPConfig(schedule.smtpConfigId);\n                    //console.log('SMTP response:', smtpResponse.data); // Debug\n                    setSelectedSMTPConfig(smtpResponse.data);\n                } catch (error) {\n                    console.error('Erro ao buscar configuração SMTP:', error);\n                    toast({\n                        title: \"Aviso\",\n                        description: \"Não foi possível carregar as configurações SMTP\",\n                        variant: \"destructive\",\n                    });\n                }\n            }\n\n            // Buscar detalhes dos destinatários\n            if (schedule.destinatarios?.length > 0) {\n                try {\n                    const destinatariosResponse = await apiService.getRecipientsByIds(schedule.destinatarios);\n\n                    if (destinatariosResponse.data.success) {\n                        setSelectedDestinatarios(destinatariosResponse.data.data);\n                        /*setValue(\"destinatarios\", destinatariosResponse.data.data.map((destinatario: Destinatario) => ({\n                            value: destinatario._id,\n                            label: destinatario.email\n                        })));*/\n                    } else {\n                        console.error('Erro ao buscar destinatários:', destinatariosResponse.data.message);\n                        toast({\n                            title: \"Aviso\",\n                            description: \"Não foi possível carregar os detalhes dos destinatários\",\n                            variant: \"destructive\",\n                        });\n                    }\n                } catch (error) {\n                    console.error('Erro ao buscar destinatários:', error);\n                    toast({\n                        title: \"Aviso\",\n                        description: \"Não foi possível carregar os detalhes dos destinatários\",\n                        variant: \"destructive\",\n                    });\n                }\n            } else {\n                setSelectedDestinatarios([]);\n            }\n        } catch (error) {\n            console.error('Erro ao buscar detalhes do agendamento:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Não foi possível carregar todos os detalhes do agendamento\",\n                variant: \"destructive\",\n            });\n        }\n    };\n\n    // Função para abrir o dialog de detalhes\n    const openDetailsDialog = async (schedule: Schedule) => {\n        console.log('Abrindo dialog de detalhes:', schedule);\n        setSelectedSchedule(schedule);\n        setShowDetailsDialog(true);\n        // Limpar estados anteriores\n        setSelectedNPrintingServer(null);\n        setSelectedSMTPConfig(null);\n        setSelectedDestinatarios([]);\n        // Buscar novos dados\n        await fetchScheduleDetails(schedule);\n    };\n\n    const openEditDialog = async (schedule: Schedule) => {\n        setIsLoading(true); // 🔄 Inicia o carregamento\n        setSelectedSchedule(schedule);\n        //console.log('Schedule:', JSON.stringify(schedule));\n        setValue(\"name\", schedule.name);\n        setValue(\"serverOriginType\", schedule.serverOriginType);\n        setValue(\"serverOrigin\", schedule.serverOrigin);\n        setValue(\"appDownloadId\", schedule.appDownloadId);\n        setValue(\"appDownloadName\", schedule.appDownloadName);\n        setValue(\"serverDestination\", schedule.serverDestination);\n        setValue(\"appReplaceId\", schedule.appReplaceId);\n        setValue(\"appReplaceName\", schedule.appReplaceName);\n        setValue(\"taskId\", schedule.taskId);\n        setValue(\"taskName\", schedule.taskName);\n        setValue(\"nprintingServerId\", schedule.nprintingServerId);\n        ////console.log('NPrinting Server:', schedule.nprintingServerId);\n        setValue(\"nprintingTaskId\", schedule.nprintingTaskId);\n        setValue(\"nprintingTaskName\", schedule.nprintingTaskName);\n        setValue(\"smtpConfigId\", schedule.smtpConfigId);\n        setValue(\"scheduleType\", schedule.scheduleType);\n        setValue(\"isActive\", schedule.isActive);\n        if (schedule.scheduleType !== \"personalizado\") {\n            setValue(\"scheduleTime\", schedule.scheduleTime);\n        } else {\n            setCustomSchedules(schedule.customSchedule || []);\n        }\n\n        // Buscar destinatários atualizados\n        try {\n            //console.log(\"🚀 Iniciando busca de destinatários...\");\n            //console.log(\"🔍 IDs dos destinatários enviados:\", schedule.destinatarios);\n\n            const response = await apiService.getRecipientsByIds(schedule.destinatarios);\n            //console.log(\"✅ Resposta recebida:\", response.data);\n\n            if (response.data.success) {\n                //console.log(\"📩 Destinatários encontrados:\", response.data.data);\n\n                const destinatariosOptions = response.data.data.map((dest: any) => ({\n                    value: dest._id,\n                    label: dest.email\n                }));\n                //setDestinatarios(destinatariosOptions);\n                //console.log(\"🎯 Destinatários formatados:\", destinatariosOptions);\n\n                // Definir os destinatários selecionados\n                //const selectedDestinatarios = schedule.destinatarios.map(dest => ({\n                //    value: dest.value,\n                //    label: dest.label\n                //}));\n                setValue(\"destinatarios\", destinatariosOptions);\n                //console.log(\"✅ Destinatários selecionados definidos:\", selectedDestinatarios);\n            } else {\n                console.warn(\"⚠️ Erro na resposta da API:\", response.data.message);\n                toast({\n                    title: \"Erro\",\n                    description: response.data.message || \"Falha ao carregar lista de destinatários.\",\n                    variant: \"destructive\",\n                });\n            }\n\n        } catch (error) {\n            console.error(\"❌ Erro ao carregar destinatários:\", error);\n            toast({\n                title: \"Erro\",\n                description: \"Falha ao carregar lista de destinatários.\",\n                variant: \"destructive\",\n            });\n        }\n        setShowEditDialog(true);\n    };\n\n    // Função para buscar destinatários disponíveis\n    /*const fetchAvailableDestinatarios = async (companyId: string) => {\n        try {\n            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/destinatarios/list/${companyId}`);\n            const data = await response.json();\n\n            if (data.success) {\n                setAvailableDestinatarios(data.data);\n            } else {\n                console.error('Erro ao buscar destinatários:', data.message);\n            }\n        } catch (error) {\n            console.error('Erro ao buscar destinatários:', error);\n        }\n    };*/\n\n    // Efeito para carregar destinatários quando o modal de edição é aberto\n    /*useEffect(() => {\n        if (editScheduleData?.serverOrigin?.company?._id) {\n            fetchAvailableDestinatarios(editScheduleData.serverOrigin.company._id);\n        }\n    }, [editScheduleData]);\n\n    // Função para adicionar um novo agendamento personalizado\n    const addCustomSchedule = () => {\n        if (selectedWeekDays.length > 0 && customTime) {\n            setEditFormData(prev => ({\n                ...prev,\n                customSchedule: [\n                    ...prev.customSchedule,\n                    { weekDays: selectedWeekDays, time: customTime }\n                ]\n            }));\n            setSelectedWeekDays([]);\n            setCustomTime(\"\");\n        }\n    };*/\n\n    // Função para remover um agendamento personalizado\n    const removeCustomSchedule = (index: number) => {\n        const updatedSchedules = [...customSchedules];\n        updatedSchedules.splice(index, 1);\n        setCustomSchedules(updatedSchedules);\n    };\n\n    // 🔹 Submissão do formulário de edição\n    const onSubmit = async (formData: ScheduleFormData) => {\n        setLoading(true);\n        try {\n            // Validações básicas\n\n            if (!formData.name) {\n                throw new Error(\"Nome do agendamento é obrigatório\");\n            }\n\n            if (!formData.serverOriginType) {\n                throw new Error(\"Tipo de servidor de origem é obrigatório\");\n            }\n\n            if (!formData.serverOrigin || !formData.appDownloadId || !formData.appDownloadName) {\n                throw new Error(\"Servidor de origem, ID e nome do aplicativo para download são obrigatórios\");\n            }\n\n            if (!formData.serverDestination || !formData.appReplaceId || !formData.appReplaceName) {\n                throw new Error(\"Servidor de destino, ID e nome do aplicativo para replace são obrigatórios\");\n            }\n\n            if (!formData.taskId || !formData.taskName) {\n                throw new Error(\"Task para execução e nome são obrigatórios\");\n            }\n\n            if (!formData.nprintingServerId._id || !formData.nprintingTaskId || !formData.nprintingTaskName) {\n                throw new Error(\"Servidor NPrinting, ID e nome da task NPrinting são obrigatórios\");\n            }\n\n            if (!formData.smtpConfigId) {\n                throw new Error(\"Remetente é obrigatório\");\n            }\n\n            if (!formData.destinatarios || formData.destinatarios.length === 0) {\n                throw new Error(\"Selecione pelo menos um destinatário\");\n            }\n\n            if (!formData.scheduleType) {\n                throw new Error(\"Tipo de agendamento é obrigatório\");\n            }\n\n            // Validação específica para cada tipo de agendamento\n            if (formData.scheduleType !== \"personalizado\" && !formData.scheduleTime) {\n                throw new Error(\"Horário de execução é obrigatório\");\n            }\n\n            if (formData.scheduleType === \"personalizado\" && (!customSchedules || customSchedules.length === 0)) {\n                throw new Error(\"Adicione pelo menos um agendamento personalizado\");\n            }\n\n            // Estrutura do payload\n            const updatedData = {\n                name: formData.name,\n                serverOriginType: formData.serverOriginType,\n                serverOrigin: {\n                    _id: formData.serverOrigin._id,\n                    name: formData.serverOrigin.name\n                },\n                appDownloadId: formData.appDownloadId,\n                appDownloadName: formData.appDownloadName,\n                serverDestination: {\n                    _id: formData.serverDestination._id,\n                    name: formData.serverDestination.name\n                },\n                appReplaceId: formData.appReplaceId,\n                appReplaceName: formData.appReplaceName,\n                taskId: formData.taskId,\n                taskName: formData.taskName,\n                nprintingServerId: formData.nprintingServerId._id,\n                nprintingTaskId: formData.nprintingTaskId,\n                nprintingTaskName: formData.nprintingTaskName,\n                smtpConfigId: formData.smtpConfigId,\n                destinatarios: formData.destinatarios.map(dest => dest.value),\n                scheduleType: formData.scheduleType,\n                scheduleTime: formData.scheduleType !== \"personalizado\" ? formData.scheduleTime : undefined,\n                customSchedule: formData.scheduleType === \"personalizado\" ? customSchedules.map(schedule => ({\n                    weekDays: schedule.weekDays,\n                    time: schedule.time\n                })) : undefined,\n                isActive: formData.isActive\n            };\n\n            //console.log(\"updatedData sendo enviado:\", updatedData); // Log para debug\n\n            const response = await apiService.updateSchedule(selectedSchedule?._id!, updatedData);\n\n            if (response.data.success) {\n                toast({\n                    title: \"Sucesso\",\n                    description: \"Agendamento atualizado com sucesso\",\n                });\n                setShowEditDialog(false);\n                fetchSchedules(); // Atualiza a lista\n                // Limpar formulário após sucesso\n                resetForm();\n            } else {\n                toast({\n                    title: \"Erro\",\n                    description: response.data.message || \"Não foi possível atualizar o agendamento\",\n                    variant: \"destructive\",\n                });\n            }\n\n\n        } catch (error: any) {\n            console.error(\"Erro ao criar agendamento:\", error);\n            const errorMessage = error.response?.data?.message || error.message || \"Ocorreu um erro inesperado.\";\n            toast({\n                title: \"Erro ao criar agendamento\",\n                description: errorMessage,\n                variant: \"destructive\"\n            });\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleExecuteClick = (scheduleId: string) => {\n        setScheduleToExecute(scheduleId);\n    };\n\n    const handleConfirmExecution = async () => {\n        if (scheduleToExecute) {\n            setScheduleToExecute(null); // Fecha o modal antes de executar\n            await executeSchedule(scheduleToExecute);\n        }\n    };\n\n\n\n    return (\n        <div className=\"container mx-auto py-6\">\n            <Card className=\"m-5\">\n                <CardHeader>\n                    <CardTitle>Lista de Agendamentos</CardTitle>\n                </CardHeader>\n                <CardContent>\n                    {loading ? (\n                        <div className=\"flex justify-center items-center h-32\">\n                            <Loader2 className=\"h-8 w-8 animate-spin\" />\n                        </div>\n                    ) : (\n                        <Table>\n                            <TableHeader>\n                                <TableRow>\n                                    {/*<TableHead>Servidor Origem</TableHead>\n                                    <TableHead>App Download</TableHead>\n                                    <TableHead>Servidor Destino</TableHead>\n                                    <TableHead>App Replace</TableHead>*/}\n                                    <TableHead>Nome</TableHead>\n                                    <TableHead>Tipo</TableHead>\n                                    <TableHead>Última Execução</TableHead>\n                                    <TableHead>Próxima Execução</TableHead>\n                                    <TableHead>Status</TableHead>\n                                    <TableHead>Ações</TableHead>\n                                </TableRow>\n                            </TableHeader>\n                            <TableBody>\n                                {schedules.map((schedule) => (\n                                    <TableRow key={schedule._id}>\n                                        {/*<TableCell>{schedule.serverOrigin.name}</TableCell>\n                                        <TableCell>\n                                            <div className=\"text-sm\">\n                                                <p>ID: {schedule.appDownloadId || 'N/A'}</p>\n                                                <p>Nome: {schedule.appDownloadName || 'N/A'}</p>\n                                            </div>\n                                        </TableCell>\n                                        <TableCell>{schedule.serverDestination.name}</TableCell>\n                                        <TableCell>\n                                            <div className=\"text-sm\">\n                                                <p>ID: {schedule.appReplaceId || 'N/A'}</p>\n                                                <p>Nome: {schedule.appReplaceName || 'N/A'}</p>\n                                            </div>\n                                        </TableCell>*/}\n                                        <TableCell>{schedule.name}</TableCell>\n                                        <TableCell>{formatScheduleType(schedule.scheduleType)}</TableCell>\n                                        <TableCell>{formatDate(schedule.lastExecution)}</TableCell>\n                                        <TableCell>{formatDate(schedule.nextExecution)}</TableCell>\n                                        <TableCell>\n                                            <div className=\"flex flex-col gap-1\">\n                                                <Badge variant={schedule.isActive ? \"default\" : \"secondary\"} className=\"flex justify-center\">\n                                                    {schedule.isActive ? \"Ativo\" : \"Inativo\"}\n                                                </Badge>\n                                                {executionStatus[schedule._id]?.isExecuting && (\n                                                    <Badge variant=\"outline\" className=\"animate-pulse flex justify-center text-center\">\n                                                        Em execução há {calculateExecutionDuration(executionStatus[schedule._id].executionStartTime!)}\n                                                    </Badge>\n                                                )}\n                                            </div>\n                                        </TableCell>\n                                        <TableCell>\n                                            <div className=\"flex gap-2\">\n                                                <Button\n                                                    variant=\"outline\"\n                                                    size=\"icon\"\n                                                    onClick={() => openHistory(schedule)}\n                                                    title=\"Ver histórico\"\n                                                >\n                                                    <History className=\"h-4 w-4\" />\n                                                </Button>\n                                                <Button\n                                                    variant=\"outline\"\n                                                    size=\"icon\"\n                                                    onClick={() => openEditDialog(schedule)}\n                                                    title=\"Editar\"\n                                                    disabled={executionStatus[schedule._id]?.isExecuting}\n                                                >\n                                                    <Edit className=\"h-4 w-4\" />\n                                                </Button>\n                                                <Button\n                                                    variant=\"outline\"\n                                                    size=\"icon\"\n                                                    onClick={() => handleExecuteClick(schedule._id)}\n                                                    disabled={!checkedSchedules.has(schedule._id) || executionStatus[schedule._id]?.isExecuting}\n                                                    title=\"Executar agora\"\n                                                >\n                                                    {!checkedSchedules.has(schedule._id) || executionStatus[schedule._id]?.isExecuting ? (\n                                                        <Loader2 className=\"h-4 w-4 animate-spin\" />\n                                                    ) : (\n                                                        <Play className=\"h-4 w-4\" />\n                                                    )}\n                                                </Button>\n                                                {executionStatus[schedule._id]?.isExecuting && (\n                                                    <Button\n                                                        variant=\"outline\"\n                                                        size=\"icon\"\n                                                        onClick={() => openLogsDialog(schedule)}\n                                                        title=\"Ver logs em tempo real\"\n                                                    >\n                                                        <Terminal className=\"h-4 w-4\" />\n                                                    </Button>\n                                                )}\n                                                <Button\n                                                    variant=\"outline\"\n                                                    size=\"icon\"\n                                                    onClick={() => openDetailsDialog(schedule)}\n                                                    title=\"Ver detalhes\"\n                                                >\n                                                    <Info className=\"h-4 w-4\" />\n                                                </Button>\n                                                <Button\n                                                    variant=\"destructive\"\n                                                    size=\"icon\"\n                                                    onClick={() => {\n                                                        setSelectedSchedule(schedule);\n                                                        setShowDeleteDialog(true);\n                                                    }}\n                                                    title=\"Excluir\"\n                                                    disabled={executionStatus[schedule._id]?.isExecuting}\n                                                >\n                                                    <Trash2 className=\"h-4 w-4\" />\n                                                </Button>\n                                            </div>\n                                        </TableCell>\n                                    </TableRow>\n                                ))}\n                            </TableBody>\n                        </Table>\n                    )}\n                </CardContent>\n            </Card>\n\n            {/* Dialog do Histórico */}\n            <Dialog open={showHistoryDialog} onOpenChange={setShowHistoryDialog}>\n                <DialogContent className=\"max-w-3xl max-h-[80vh] overflow-y-auto\">\n                    <DialogHeader>\n                        <DialogTitle>Histórico de Execuções</DialogTitle>\n                        <DialogDescription>\n                            Detalhes das execuções do agendamento\n                        </DialogDescription>\n                    </DialogHeader>\n                    {selectedSchedule?.executionHistory?.length === 0 ? (\n                        <div className=\"text-center py-4 text-muted-foreground\">\n                            Nenhuma execução registrada\n                        </div>\n                    ) : (\n                        [...(selectedSchedule?.executionHistory || [])]\n                            .sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime())\n                            .map((execution, index) => (\n                                <Card key={index} className=\"mb-4\">\n                                    <CardHeader>\n                                        <CardTitle className=\"text-sm flex justify-between items-center\">\n                                            <span className=\"flex items-center gap-2\">\n                                                <Clock className=\"h-4 w-4\" />\n                                                Execução em {formatDate(execution.startTime)}\n                                            </span>\n                                            <Badge variant={execution.success ? \"default\" : \"destructive\"}>\n                                                {execution.success ? \"Sucesso\" : \"Falha\"}\n                                            </Badge>\n                                        </CardTitle>\n                                    </CardHeader>\n                                    <CardContent>\n                                        <div className=\"space-y-2\">\n                                            {execution.steps.map((step, stepIndex) => (\n                                                <div key={stepIndex} className=\"flex items-center gap-2\">\n                                                    <Badge variant={step.status === \"concluído\" ? \"default\" : \"secondary\"} className=\"min-w-[75px] flex justify-center\">\n                                                        Passo {step.step}\n                                                    </Badge>\n                                                    <span>{step.message}</span>\n                                                </div>\n                                            ))}\n                                            {execution.error && (\n                                                <div className=\"mt-2 p-2 bg-red-50 text-red-700 rounded\">\n                                                    <p className=\"font-semibold\">Erro:</p>\n                                                    <p>{execution.error.message}</p>\n                                                </div>\n                                            )}\n                                        </div>\n                                    </CardContent>\n                                </Card>\n                            ))\n                    )}\n                </DialogContent>\n            </Dialog>\n\n            {/* Dialog de Confirmação de Exclusão */}\n            <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\n                <AlertDialogContent>\n                    <AlertDialogHeader>\n                        <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>\n                        <AlertDialogDescription>\n                            Tem certeza que deseja excluir este agendamento? Esta ação não pode ser desfeita.\n                        </AlertDialogDescription>\n                    </AlertDialogHeader>\n                    <AlertDialogFooter>\n                        <AlertDialogCancel>Cancelar</AlertDialogCancel>\n                        <AlertDialogAction onClick={deleteSchedule}>\n                            Confirmar\n                        </AlertDialogAction>\n                    </AlertDialogFooter>\n                </AlertDialogContent>\n            </AlertDialog>\n\n            {/* Dialog de Logs em Tempo Real */}\n            <Dialog open={showLogsDialog} onOpenChange={closeLogsDialog}>\n                <DialogContent className=\"max-w-3xl max-h-[80vh] overflow-y-auto\">\n                    <DialogHeader>\n                        <DialogTitle>Logs em Tempo Real</DialogTitle>\n                        <DialogDescription>\n                            {selectedSchedule && executionStatus[selectedSchedule._id]?.executionStartTime && (\n                                <div className=\"space-y-4\">\n                                    <span>\n                                        Execução iniciada há {calculateExecutionDuration(executionStatus[selectedSchedule._id].executionStartTime!)}\n                                    </span>\n                                    <div className=\"space-y-4 mt-4\">\n                                        {/* Barra de Progresso Geral */}\n                                        {renderProgressBar(calculateOverallProgress(progressStatus), \"Progresso Geral\")}\n\n                                        {/* Barras de Progresso Individuais */}\n                                        <div className=\"grid grid-cols-2 gap-4\">\n                                            {renderProgressBar(progressStatus.download, \"Download\")}\n                                            {renderProgressBar(progressStatus.upload, \"Upload\")}\n                                            {renderProgressBar(progressStatus.replace, \"Substituição\")}\n                                            {renderProgressBar(progressStatus.task, \"Execução da Task\")}\n                                        </div>\n                                    </div>\n                                </div>\n                            )}\n                        </DialogDescription>\n                    </DialogHeader>\n                    <div className=\"bg-black text-green-400 p-4 rounded font-mono text-sm mt-4\">\n                        {realtimeLogs.length === 0 ? (\n                            <div className=\"text-gray-500\">Aguardando logs...</div>\n                        ) : (\n                            <div className=\"space-y-2\">\n                                {realtimeLogs.reduce((acc: JSX.Element[], log, index) => {\n                                    // Extrai a parte base da mensagem (sem a porcentagem)\n                                    const baseMessage = log.message.replace(/: \\d+%$/, '');\n                                    \n                                    // Verifica se já existe uma mensagem similar\n                                    const similarMessageIndex = acc.findIndex(\n                                        (item) => item.key && \n                                        realtimeLogs[parseInt(item.key as string)]?.message.replace(/: \\d+%$/, '') === baseMessage\n                                    );\n\n                                    // Se encontrou mensagem similar e tem progresso, atualiza ela\n                                    if (similarMessageIndex !== -1 && log.progress !== undefined) {\n                                        acc[similarMessageIndex] = (\n                                            <div key={index} className=\"flex items-start gap-2\">\n                                                <span className=\"text-gray-500 whitespace-nowrap\">\n                                                    [{new Date(log.timestamp).toLocaleTimeString()}]\n                                                </span>\n                                                <div className=\"flex-1\">\n                                                    <span>{log.message}</span>\n                                                    {log.progress !== undefined && (\n                                                        <div className=\"h-1 bg-gray-700 rounded-full mt-1 overflow-hidden\">\n                                                            <div\n                                                                className=\"h-full bg-green-400 rounded-full transition-all duration-300\"\n                                                                style={{ width: `${log.progress}%` }}\n                                                            />\n                                                        </div>\n                                                    )}\n                                                </div>\n                                            </div>\n                                        );\n                                    } \n                                    // Se não encontrou mensagem similar ou não tem progresso, adiciona nova\n                                    else if (similarMessageIndex === -1) {\n                                        acc.push(\n                                            <div key={index} className=\"flex items-start gap-2\">\n                                                <span className=\"text-gray-500 whitespace-nowrap\">\n                                                    [{new Date(log.timestamp).toLocaleTimeString()}]\n                                                </span>\n                                                <div className=\"flex-1\">\n                                                    <span>{log.message}</span>\n                                                    {log.progress !== undefined && (\n                                                        <div className=\"h-1 bg-gray-700 rounded-full mt-1 overflow-hidden\">\n                                                            <div\n                                                                className=\"h-full bg-green-400 rounded-full transition-all duration-300\"\n                                                                style={{ width: `${log.progress}%` }}\n                                                            />\n                                                        </div>\n                                                    )}\n                                                </div>\n                                            </div>\n                                        );\n                                    }\n                                    return acc;\n                                }, [])}\n                            </div>\n                        )}\n                    </div>\n                </DialogContent>\n            </Dialog>\n\n            {/* Dialog de Detalhes */}\n            <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>\n                <DialogContent className=\"max-w-3xl\">\n                    <DialogHeader>\n                        <DialogTitle>Detalhes do Agendamento</DialogTitle>\n                        <DialogDescription>\n                            {selectedSchedule?.name}\n                        </DialogDescription>\n                    </DialogHeader>\n                    {selectedSchedule && (\n                        <div className=\"grid grid-cols-2 gap-4\">\n                            <div className=\"space-y-4\">\n                                <div>\n                                    <h3 className=\"font-semibold mb-2\">Tipo de Servidor de Origem</h3>\n                                    <p className=\"text-sm\">{selectedSchedule.serverOriginType === \"qrs\" ? \"Qlik Sense Enterprise Client Manager\" : \"Qlik Sense Cloud\"}</p>\n                                </div>\n                                <div>\n                                    <h3 className=\"font-semibold mb-2\">Servidor de Origem</h3>\n                                    <p className=\"text-sm\">{selectedSchedule.serverOrigin.name}</p>\n                                </div>\n                                <div>\n                                    <h3 className=\"font-semibold mb-2\">App para Download</h3>\n                                    <div className=\"text-sm\">\n                                        <p>ID: {selectedSchedule.appDownloadId || 'N/A'}</p>\n                                        <p>Nome: {selectedSchedule.appDownloadName || 'N/A'}</p>\n                                    </div>\n                                </div>\n                                <div>\n                                    <h3 className=\"font-semibold mb-2\">Servidor de Destino</h3>\n                                    <p className=\"text-sm\">{selectedSchedule.serverDestination.name}</p>\n                                </div>\n                                <div>\n                                    <h3 className=\"font-semibold mb-2\">App para Replace</h3>\n                                    <div className=\"text-sm\">\n                                        <p>ID: {selectedSchedule.appReplaceId || 'N/A'}</p>\n                                        <p>Nome: {selectedSchedule.appReplaceName || 'N/A'}</p>\n                                    </div>\n                                </div>\n                                <div>\n                                    <h3 className=\"font-semibold mb-2\">Task de Execução</h3>\n                                    <div className=\"text-sm\">\n                                        <p>ID: {selectedSchedule.taskId || 'N/A'}</p>\n                                        <p>Nome: {selectedSchedule.taskName || 'N/A'}</p>\n                                    </div>\n                                </div>\n                                <div>\n                                    <h3 className=\"font-semibold mb-2\">Servidor NPrinting</h3>\n                                    <p className=\"text-sm\">\n                                        {selectedNPrintingServer ? (\n                                            selectedNPrintingServer.name\n                                        ) : (\n                                            selectedSchedule?.nprintingServerId ? 'Carregando...' : 'N/A'\n                                        )}\n                                    </p>\n                                </div>\n                                <div>\n                                    <h3 className=\"font-semibold mb-2\">Configuração SMTP</h3>\n                                    {selectedSMTPConfig ? (\n                                        <div className=\"text-sm\">\n                                            <p>Empresa: {selectedSMTPConfig.company.name}</p>\n                                            <p>Remetente: {selectedSMTPConfig.from.name}</p>\n                                            <p>E-mail: {selectedSMTPConfig.from.email}</p>\n                                        </div>\n                                    ) : (\n                                        <p className=\"text-sm\">Carregando...</p>\n                                    )}\n                                </div>\n                            </div>\n                            <div className=\"space-y-4\">\n                                <div>\n                                    <h3 className=\"font-semibold mb-2\">Task NPrinting</h3>\n                                    <div className=\"text-sm\">\n                                        <p>ID: {selectedSchedule.nprintingTaskId || 'N/A'}</p>\n                                        <p>Nome: {selectedSchedule.nprintingTaskName || 'N/A'}</p>\n                                    </div>\n                                </div>\n                                <div>\n                                    <h3 className=\"font-semibold mb-2\">Tipo de Agendamento</h3>\n                                    <p className=\"text-sm\">{formatScheduleType(selectedSchedule.scheduleType)}</p>\n                                    {selectedSchedule.scheduleType !== 'personalizado' ? (\n                                        <p className=\"text-sm text-muted-foreground\">Horário: {selectedSchedule.scheduleTime}</p>\n                                    ) : (\n                                        <div className=\"mt-2\">\n                                            {selectedSchedule.customSchedule?.map((schedule, index) => (\n                                                <p key={index} className=\"text-sm text-muted-foreground\">\n                                                    {formatWeekDays(schedule.weekDays)} às {schedule.time}\n                                                </p>\n                                            ))}\n                                        </div>\n                                    )}\n                                </div>\n                                <div>\n                                    <h3 className=\"font-semibold mb-2\">Destinatários (Empresa de Origem)</h3>\n                                    <div className=\"max-h-24 overflow-y-auto\">\n                                        {selectedDestinatarios.length > 0 ? (\n                                            selectedDestinatarios.map((destinatario) => (\n                                                <div key={destinatario._id} className=\"text-sm mb-1\">\n                                                    <p>Nome: {destinatario.name}</p>\n                                                    <p className=\"text-muted-foreground\">E-mail: {destinatario.email}</p>\n                                                </div>\n                                            ))\n                                        ) : (\n                                            <p className=\"text-sm\">Carregando destinatários...</p>\n                                        )}\n                                    </div>\n                                </div>\n                                <div>\n                                    <h3 className=\"font-semibold mb-2\">Status</h3>\n                                    <div className=\"space-y-2\">\n                                        <Badge variant={selectedSchedule.isActive ? \"default\" : \"secondary\"}>\n                                            {selectedSchedule.isActive ? \"Ativo\" : \"Inativo\"}\n                                        </Badge>\n                                        {executionStatus[selectedSchedule._id]?.isExecuting && (\n                                            <Badge variant=\"outline\" className=\"animate-pulse ml-2\">\n                                                Em execução\n                                            </Badge>\n                                        )}\n                                    </div>\n                                </div>\n                                <div>\n                                    <h3 className=\"font-semibold mb-2\">Execuções</h3>\n                                    <p className=\"text-sm\">Última: {formatDate(selectedSchedule.lastExecution)}</p>\n                                    <p className=\"text-sm\">Próxima: {formatDate(selectedSchedule.nextExecution)}</p>\n                                </div>\n                            </div>\n                        </div>\n                    )}\n                </DialogContent>\n            </Dialog>\n\n            {/* Dialog de Edição */}\n            <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>\n                <DialogContent className=\"max-w-4xl max-h-[80vh] overflow-y-auto\">\n                    <DialogHeader>\n                        <DialogTitle>Editar Agendamento</DialogTitle>\n                        <DialogDescription>\n                            Atualize as informações do agendamento\n                        </DialogDescription>\n                    </DialogHeader>\n                    {\n                        selectedSchedule && (\n                            <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n\n                                {isLoading && (\n                                    <div className=\"absolute w-[99.3%] -ml-[20px] h-screen z-15 bg-gray-500 bg-opacity-50 rounded-lg\" style={{ zIndex: 15 }}>\n                                        <div className=\"absolute top-[10%] left-[37%] z-10 flex flex-col items-center justify-center bg-white p-4 rounded-lg shadow-lg text-[20px]\">\n                                            <span className=\"text-gray-600\">Carregando dados...</span>\n                                            <div className=\"flex justify-center items-center h-32\">\n                                                <Loader2 className=\"h-16 w-16 animate-spin\" />\n                                            </div>\n                                        </div>\n                                    </div>\n                                )}\n\n                                {/* 🔹 Nome do Agendamento */}\n                                <div>\n                                    <Label>Nome do Agendamento</Label>\n                                    <Controller\n                                        name=\"name\"\n                                        control={control}\n                                        defaultValue={selectedSchedule?.name}\n                                        render={({ field }) => (\n                                            <Input\n                                                placeholder=\"Digite o nome do agendamento\"\n                                                {...field}\n                                            />\n                                        )}\n                                    />\n                                </div>\n\n                                {/* 🔹 Servidor de Origem */}\n                                <div>\n                                    <Label>Tipo de Servidor de Origem</Label>\n                                    <Controller\n                                        name=\"serverOriginType\"\n                                        control={control}\n                                        defaultValue={selectedSchedule?.serverOriginType}\n                                        render={({ field }) => (\n                                            <Select\n                                                value={field.value}\n                                                onValueChange={(value) => {\n                                                    field.onChange(value);\n                                                    setValue(\"serverOrigin\", { _id: \"\", name: \"\" });\n                                                    setValue(\"appDownloadId\", \"\");\n                                                    setValue(\"appDownloadName\", \"\");\n                                                    setAppsOrigin([]);\n                                                }}\n                                            >\n                                                <SelectTrigger>\n                                                    <SelectValue placeholder=\"Selecione um tipo de servidor\" />\n                                                </SelectTrigger>\n                                                <SelectContent>\n                                                    <SelectItem value=\"qrs\">Qlik Sense Enterprise Client Manager</SelectItem>\n                                                    <SelectItem value=\"cloud\">Qlik Sense Cloud</SelectItem>\n                                                </SelectContent>\n                                            </Select>\n                                        )}\n                                    />\n                                </div>\n\n                                {/* 🔹 Servidor de Origem */}\n                                <div>\n                                    <Label>Servidor de Origem</Label>\n                                    <Controller\n                                        name=\"serverOrigin\"\n                                        control={control}\n                                        defaultValue={selectedSchedule?.serverOrigin}\n                                        render={({ field }) => (\n                                            <Select\n                                                value={field.value?._id || \"\"}\n                                                disabled={!serverOriginType}\n                                                onValueChange={(value) => {\n                                                    field.onChange({ _id: value, name: servers.find(s => s._id === value)?.name || \"\" });\n                                                    setAppsOrigin([]);\n                                                    setValue(\"appDownloadId\", \"\");\n                                                    setValue(\"appDownloadName\", \"\");\n                                                }}\n                                            >\n                                                <SelectTrigger>\n                                                    <SelectValue placeholder=\"Selecione um servidor\" />\n                                                </SelectTrigger>\n                                                <SelectContent>\n                                                    {servers.map((server) => (\n                                                        <SelectItem key={server._id} value={server._id}>\n                                                            {server.name}\n                                                        </SelectItem>\n                                                    ))}\n                                                </SelectContent>\n                                            </Select>\n                                        )}\n                                    />\n                                </div>\n\n                                {/* 🔹 Filtrar por Stream */}\n                                <div className=\"mb-4\">\n                                    <Label>Filtrar App de Download por Stream</Label>\n                                    <Select\n                                        onValueChange={setSelectedStream}\n                                        value={selectedStream}\n                                        disabled={!serverOrigin?._id} // ✅ desabilita se não tiver servidor\n                                    >\n                                        <SelectTrigger>\n                                            <SelectValue placeholder=\"Selecione a stream\" />\n                                        </SelectTrigger>\n                                        <SelectContent>\n                                            {isLoadingAppsOrigin ? (\n                                                <SelectItem value=\"loading\" disabled className=\"flex justify-center items-center gap-2\">\n                                                    Carregando streams...\n                                                </SelectItem>\n                                            ) : streamOptions.length <= 0 ?\n                                                <SelectItem value=\"Todos\">Todos</SelectItem>\n                                                :\n                                                streamOptions.map(stream => (\n                                                    <SelectItem key={stream} value={stream}>\n                                                        {stream}\n                                                    </SelectItem>\n                                                ))\n                                            }\n                                        </SelectContent>\n                                    </Select>\n                                </div>\n\n                                {/* 🔹 Aplicativo para Download */}\n                                <div>\n                                    <Label>Aplicativo para Download</Label>\n                                    <Controller\n                                        name=\"appDownloadId\"\n                                        control={control}\n                                        defaultValue={selectedSchedule?.appDownloadId}\n                                        render={({ field }) => (\n                                            <AsyncSelect<SelectOption>\n                                                value={appsOrigin.find(option => option.value === field.value) || null}\n                                                instanceId=\"app-download-select\"\n                                                cacheOptions\n                                                defaultOptions={appsOrigin.filter(app =>\n                                                    selectedStream === \"Todos\" || app.stream === selectedStream\n                                                )}\n                                                loadOptions={(inputValue: string, callback: (options: SelectOption[]) => void) => {\n                                                    const filteredOptions = appsOrigin.filter((option) =>\n                                                        option.label.toLowerCase().includes(inputValue.toLowerCase())\n                                                    );\n                                                    callback(filteredOptions);\n                                                }}\n                                                isDisabled={!serverOrigin?._id}\n                                                onChange={(option: SelectOption | null) => {\n                                                    field.onChange(option?.value);\n                                                    const name = option?.label.split(' - ID:')[0].replace('Nome: ', '') || '';\n                                                    setValue(\"appDownloadName\", name);\n                                                }}\n                                                placeholder=\"Selecione um aplicativo\"\n                                                /** 🔹 Mensagem enquanto carrega */\n                                                isLoading={isLoadingAppsOrigin}\n                                                loadingMessage={() => \"Carregando aplicativos...\"}\n                                                noOptionsMessage={() => \"Nenhum aplicativo encontrado\"}\n                                            />\n                                        )}\n                                    />\n                                </div>\n\n                                {/* 🔹 Servidor de Destino */}\n                                <div>\n                                    <Label>Servidor de Destino</Label>\n                                    <Controller\n                                        name=\"serverDestination\"\n                                        control={control}\n                                        defaultValue={selectedSchedule?.serverDestination}\n                                        render={({ field }) => (\n                                            <Select\n                                                value={field.value?._id || \"\"}\n                                                onValueChange={(value) => {\n                                                    field.onChange({ _id: value, name: serversDestination.find(s => s._id === value)?.name || \"\" });\n                                                    setAppsDestination([]);\n                                                    setTasks([]);\n                                                    setValue(\"appReplaceId\", \"\");\n                                                    setValue(\"appReplaceName\", \"\");\n                                                    setValue(\"taskId\", \"\");\n                                                    setValue(\"taskName\", \"\");\n                                                }}\n                                            >\n                                                <SelectTrigger>\n                                                    <SelectValue placeholder=\"Selecione um servidor\" />\n                                                </SelectTrigger>\n                                                <SelectContent>\n                                                    {serversDestination.map((server) => (\n                                                        <SelectItem key={server._id} value={server._id}>\n                                                            {server.name}\n                                                        </SelectItem>\n                                                    ))}\n                                                </SelectContent>\n                                            </Select>\n                                        )}\n                                    />\n                                </div>\n\n                                {/* 🔹 Filtrar App de Replace por Stream */}\n                                <div className=\"mb-4\">\n                                    <Label>Filtrar App de Replace por Stream</Label>\n                                    <Select\n                                        onValueChange={setSelectedStreamDestino}\n                                        value={selectedStreamDestino}\n                                        disabled={!serverDestination?._id}\n                                    >\n                                        <SelectTrigger>\n                                            <SelectValue placeholder=\"Selecione a stream\" />\n                                        </SelectTrigger>\n                                        <SelectContent>\n                                            {isLoadingAppsDestino ? (\n                                                <SelectItem value=\"loading\" disabled className=\"flex justify-center items-center gap-2\">\n                                                    Carregando streams...\n                                                </SelectItem>\n                                            ) : streamOptionsDestino.length <= 0 ?\n                                                <SelectItem value=\"Todos\">Todos</SelectItem>\n                                                :\n                                                streamOptionsDestino.map(stream => (\n                                                    <SelectItem key={stream} value={stream}>{stream}</SelectItem>\n                                                ))\n                                            }\n                                        </SelectContent>\n                                    </Select>\n                                </div>\n\n                                {/* 🔹 Aplicativo para Replace */}\n                                <div>\n                                    <Label>Aplicativo para Replace</Label>\n                                    <Controller\n                                        name=\"appReplaceId\"\n                                        defaultValue={selectedSchedule?.appReplaceId}\n                                        control={control}\n                                        render={({ field }) => (\n                                            <AsyncSelect<SelectOption>\n                                                value={appsDestination.find(option => option.value === field.value) || null}\n                                                instanceId=\"app-replace-select\"\n                                                cacheOptions\n                                                defaultOptions={appsDestination.filter(app =>\n                                                    selectedStreamDestino === \"Todos\" || app.stream === selectedStreamDestino\n                                                )}\n                                                loadOptions={(inputValue: string, callback: (options: SelectOption[]) => void) => {\n                                                    const filteredOptions = appsDestination.filter((option) =>\n                                                        option.label.toLowerCase().includes(inputValue.toLowerCase())\n                                                    );\n                                                    callback(filteredOptions);\n                                                }}\n                                                isDisabled={!serverDestination?._id}\n                                                onChange={(option: SelectOption | null) => {\n                                                    field.onChange(option?.value);\n                                                    const name = option?.label.split(' - ID:')[0].replace('Nome: ', '') || '';\n                                                    setValue(\"appReplaceName\", name);\n                                                    //console.log(\"🔹 Aplicativo para Replace selecionado:\", name);\n                                                }}\n                                                placeholder=\"Selecione um aplicativo\"\n                                                /** 🔹 Mensagem enquanto carrega */\n                                                isLoading={isLoadingAppsDestino}\n                                                loadingMessage={() => \"Carregando aplicativos...\"}\n                                                noOptionsMessage={() => \"Nenhum aplicativo encontrado\"}\n                                            />\n                                        )}\n                                    />\n                                </div>\n\n                                {/* 🔹 Task para Execução */}\n                                <div>\n                                    <Label>Task para Execução</Label>\n                                    <Controller\n                                        defaultValue={selectedSchedule?.taskId}\n                                        name=\"taskId\"\n                                        control={control}\n                                        render={({ field }) => (\n                                            <AsyncSelect<SelectOption>\n                                                value={tasks.find(option => option.value === field.value) || null}\n                                                instanceId=\"task-select\"\n                                                cacheOptions\n                                                defaultOptions={tasks}\n                                                loadOptions={(inputValue: string, callback: (options: SelectOption[]) => void) => {\n                                                    const filteredOptions = tasks.filter((option) =>\n                                                        option.label.toLowerCase().includes(inputValue.toLowerCase())\n                                                    );\n                                                    callback(filteredOptions);\n                                                }}\n                                                isDisabled={!serverDestination?._id}\n                                                onChange={(option: SelectOption | null) => {\n                                                    field.onChange(option?.value);\n                                                    const name = option?.label.split(' - ID:')[0].replace('Nome: ', '') || '';\n                                                    setValue(\"taskName\", name);\n                                                }}\n                                                placeholder=\"Selecione uma Task\"\n                                            />\n                                        )}\n                                    />\n                                </div>\n\n                                {/* 🔹 Servidor NPrinting */}\n                                <div>\n                                    <Label>Servidor NPrinting</Label>\n                                    <Controller\n                                        name=\"nprintingServerId\"\n                                        defaultValue={selectedSchedule?.nprintingServerId}\n                                        control={control}\n                                        render={({ field }) => (\n                                            <Select\n                                                //defaultValue={field.value}\n                                                //value={field.value || \"\"}\n                                                value={field.value?._id || \"\"}\n                                                onValueChange={(value) => {\n                                                    field.onChange(value);\n                                                    setNPrintingTasks([]);\n                                                    setValue(\"nprintingTaskId\", \"\");\n                                                    setValue(\"nprintingTaskName\", \"\");\n                                                }}\n                                            >\n                                                <SelectTrigger>\n                                                    <SelectValue placeholder=\"Selecione um servidor NPrinting\" />\n                                                </SelectTrigger>\n                                                <SelectContent>\n                                                    {nprintingServers.map((server) => (\n                                                        <SelectItem key={server._id} value={server._id}>\n                                                            {server.name}\n                                                        </SelectItem>\n                                                    ))}\n                                                </SelectContent>\n                                            </Select>\n                                        )}\n                                    />\n                                </div>\n\n                                {/* 🔹 Task NPrinting */}\n                                <div>\n                                    <Label>Task NPrinting</Label>\n                                    <Controller\n                                        name=\"nprintingTaskId\"\n                                        defaultValue={selectedSchedule?.nprintingTaskId}\n                                        control={control}\n                                        render={({ field }) => (\n                                            <AsyncSelect<SelectOption>\n                                                value={nprintingTasks.find(option => option.value === field.value) || null}\n                                                instanceId=\"nprinting-task-select\"\n                                                cacheOptions\n                                                defaultOptions={nprintingTasks}\n                                                loadOptions={(inputValue: string, callback: (options: SelectOption[]) => void) => {\n                                                    const filteredOptions = nprintingTasks.filter((option) =>\n                                                        option.label.toLowerCase().includes(inputValue.toLowerCase())\n                                                    );\n                                                    callback(filteredOptions);\n                                                }}\n                                                isDisabled={!nprintingServerId?._id}\n                                                onChange={(option: SelectOption | null) => {\n                                                    field.onChange(option?.value);\n                                                    const name = option?.label.split(' - ID:')[0].replace('Nome: ', '') || '';\n                                                    setValue(\"nprintingTaskName\", name);\n                                                }}\n                                                placeholder=\"Selecione uma Task NPrinting\"\n                                            />\n                                        )}\n                                    />\n                                </div>\n\n                                {/* 🔹 Remetente SMTP */}\n                                <div>\n                                    <Label>Remetente</Label>\n                                    <Controller\n                                        defaultValue={selectedSchedule?.smtpConfigId}\n                                        name=\"smtpConfigId\"\n                                        control={control}\n                                        render={({ field }) => (\n                                            <Select\n                                                value={field.value || \"\"}\n                                                onValueChange={field.onChange}\n                                            >\n                                                <SelectTrigger>\n                                                    <SelectValue placeholder=\"Selecione um remetente\" />\n                                                </SelectTrigger>\n                                                <SelectContent>\n                                                    {smtpConfigs.map((config) => (\n                                                        <SelectItem key={config._id} value={config._id}>\n                                                            Empresa: {config.company.name} E-mail: {config.from.email}\n                                                        </SelectItem>\n                                                    ))}\n                                                </SelectContent>\n                                            </Select>\n                                        )}\n                                    />\n                                </div>\n\n                                {/* 🔹 Destinatários */}\n                                <div>\n                                    <Label>Destinatários (Empresa de Origem)</Label>\n                                    <Controller\n                                        name=\"destinatarios\"\n                                        control={control}\n                                        defaultValue={[]}\n                                        render={({ field }) => (\n                                            <AsyncSelect<SelectOption, true>\n                                                instanceId={`destinatarios-select-${selectedSchedule?._id}`}\n                                                isMulti\n                                                cacheOptions\n                                                value={field.value || []} // Array de objetos SelectOption\n                                                defaultOptions={destinatarios}\n                                                loadOptions={(inputValue: string, callback: (options: SelectOption[]) => void) => {\n                                                    const filteredOptions = destinatarios.filter((option) =>\n                                                        option.label.toLowerCase().includes(inputValue.toLowerCase())\n                                                    );\n                                                    callback(filteredOptions);\n                                                }}\n                                                isDisabled={!serverOrigin?._id}\n                                                onChange={(selectedOptions) => {\n                                                    field.onChange(selectedOptions || []);\n                                                }}\n                                                placeholder=\"Pesquise e selecione os destinatários\"\n                                            />\n                                        )}\n                                    />\n                                </div>\n\n                                {/* 🔹 Tipo de Agendamento */}\n                                <div>\n                                    <Label>Tipo de Agendamento</Label>\n                                    <Controller\n                                        name=\"scheduleType\"\n                                        control={control}\n                                        defaultValue={selectedSchedule?.scheduleType}\n                                        render={({ field }) => (\n                                            <Select\n                                                value={field.value || \"\"}\n                                                onValueChange={field.onChange}\n                                            >\n                                                <SelectTrigger>\n                                                    <SelectValue placeholder=\"Selecione um tipo\" />\n                                                </SelectTrigger>\n                                                <SelectContent>\n                                                    <SelectItem value=\"diario\">Diário</SelectItem>\n                                                    <SelectItem value=\"semanal\">Semanal</SelectItem>\n                                                    <SelectItem value=\"mensal\">Mensal</SelectItem>\n                                                    <SelectItem value=\"personalizado\">Personalizado</SelectItem>\n                                                </SelectContent>\n                                            </Select>\n                                        )}\n                                    />\n                                </div>\n\n                                {/* 🔹 Horário de Execução */}\n                                {scheduleType !== \"personalizado\" && (\n                                    <div>\n                                        <Label>Horário de Execução</Label>\n                                        <Controller\n                                            name=\"scheduleTime\"\n                                            control={control}\n                                            defaultValue=\"\"\n                                            render={({ field }) => (\n                                                <Input type=\"time\" {...field} value={field.value || \"\"} />\n                                            )}\n                                        />\n                                    </div>\n                                )}\n\n                                {/* 🔹 Datas e Horários Personalizados */}\n                                {scheduleType === \"personalizado\" && (\n                                    <div className=\"space-y-4\">\n                                        <Label>Dias da Semana e Horário</Label>\n\n                                        <div className=\"flex flex-col space-y-4\">\n                                            <div className=\"flex flex-col space-y-2\">\n                                                <Label>Selecione os dias da semana</Label>\n                                                <div className=\"flex gap-2 flex-wrap\">\n                                                    {weekDays.map((weekDay, index) => (\n                                                        <Button\n                                                            key={weekDay.value}\n                                                            type=\"button\"\n                                                            variant={selectedWeekDays.includes(weekDay.value) ? \"default\" : \"outline\"}\n                                                            className=\"w-10 h-10 p-0\"\n                                                            title={weekDay.label}\n                                                            onClick={() => toggleWeekDay(weekDay.value)}\n                                                        >\n                                                            {weekDay.day}\n                                                        </Button>\n                                                    ))}\n                                                </div>\n                                            </div>\n\n                                            <div className=\"flex flex-col space-y-2\">\n                                                <Label>Horário de execução</Label>\n                                                <Input\n                                                    type=\"time\"\n                                                    value={customTime || \"\"}\n                                                    onChange={(e) => setCustomTime(e.target.value)}\n                                                />\n                                            </div>\n\n                                            <Button\n                                                type=\"button\"\n                                                variant=\"outline\"\n                                                onClick={addCustomSchedules}\n                                                className=\"w-full\"\n                                            >\n                                                <PlusCircle size={18} className=\"mr-2\" />\n                                                Adicionar Agendamento\n                                            </Button>\n                                        </div>\n\n                                        {customSchedules.length > 0 && (\n                                            <div className=\"mt-4\">\n                                                <Label>Agendamentos</Label>\n                                                <div className=\"space-y-2 mt-2\">\n                                                    {customSchedules.map((schedule, index) => (\n                                                        <div key={index} className=\"flex items-center justify-between p-2 bg-gray-50 rounded-md\">\n                                                            <span>\n                                                                {formatWeekDays(schedule.weekDays)} às {schedule.time}\n                                                            </span>\n                                                            <Button\n                                                                variant=\"ghost\"\n                                                                size=\"icon\"\n                                                                type=\"button\"\n                                                                onClick={() => removeCustomSchedule(index)}\n                                                            >\n                                                                <Trash2 size={18} className=\"text-red-500\" />\n                                                            </Button>\n                                                        </div>\n                                                    ))}\n                                                </div>\n                                            </div>\n                                        )}\n                                    </div>\n                                )}\n\n                                {/* 🔹 Switch para ativar/desativar o agendamento */}\n                                <div className=\"flex items-center space-x-2\">\n                                    <Switch\n                                        checked={watch(\"isActive\")}\n                                        onCheckedChange={(checked) => setValue(\"isActive\", checked)}\n                                    />\n                                    <Label>Ativo</Label>\n                                </div>\n\n                                <Button type=\"submit\" disabled={loading} className=\"w-full\">\n                                    {loading ? \"Salvando...\" : \"Salvar Alterações\"}\n                                </Button>\n                            </form>\n                        )}\n                </DialogContent>\n            </Dialog>\n\n            <Dialog open={!!scheduleToExecute} onOpenChange={() => setScheduleToExecute(null)}>\n                <DialogContent>\n                    <DialogHeader>\n                        <DialogTitle>Confirmar Execução</DialogTitle>\n                        <DialogDescription>\n                            Tem certeza que deseja executar este agendamento agora?\n                        </DialogDescription>\n                    </DialogHeader>\n                    <DialogFooter>\n                        <Button variant=\"outline\" onClick={() => setScheduleToExecute(null)}>Cancelar</Button>\n                        <Button onClick={handleConfirmExecution}>Confirmar</Button>\n                    </DialogFooter>\n                </DialogContent>\n            </Dialog>\n        </div>\n    );\n} "], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AAEA;AAQA;AAQA;AAUA;AAOA;AACA;AAGA;AACA;AACA;AA9CA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsCA;AAtCA;AATA;;;;;;;;;;;;;;;;;;;AAsKe,SAAS;IACpB,qBACI,8OAAC,oIAAA,CAAA,iBAAc;kBACX,cAAA,8OAAC;;;;;;;;;;AAGb;AAEA,SAAS;IACL,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,YAAY;QACZ,iBAAiB;QACjB,OAAO;QACP,gBAAgB;IACpB;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2E,EAAE;IAClH,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2E,EAAE;IACxI,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAC9E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IACpF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC,CAAC;IACzF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACjE,UAAU;QACV,QAAQ;QACR,SAAS;QACT,MAAM;IACV;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAC1E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC9E,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAC/F,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAChF,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAE1E,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACvF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACvE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1E,MAAM,eAAe,MAAM;IAC3B,MAAM,mBAAmB,MAAM;IAC/B,MAAM,oBAAoB,MAAM;IAChC,MAAM,oBAAoB,MAAM;IAChC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0C,EAAE;IACjG,MAAM,eAAe,MAAM;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,WAAW;QACb;YAAE,KAAK;YAAK,OAAO;YAAW,OAAO;QAAE;QACvC;YAAE,KAAK;YAAK,OAAO;YAAW,OAAO;QAAE;QACvC;YAAE,KAAK;YAAK,OAAO;YAAS,OAAO;QAAE;QACrC;YAAE,KAAK;YAAK,OAAO;YAAU,OAAO;QAAE;QACtC;YAAE,KAAK;YAAK,OAAO;YAAU,OAAO;QAAE;QACtC;YAAE,KAAK;YAAK,OAAO;YAAS,OAAO;QAAE;QACrC;YAAE,KAAK;YAAK,OAAO;YAAU,OAAO;QAAE;KACzC;IAED,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3E,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjE,MAAM,qBAAqB,CAAC;QACxB,MAAM,YAAY,OAAO,MAAM,CAAC,WAAW,KAAK,CAAC,CAAA,SAAU,WAAW;QACtE,qEAAqE;QACrE,qDAAqD;QACrD,IAAI,WAAW;YACX,oBAAoB;YACpB,0BAA0B;YAC1B,2EAA2E;YAC3E,oFAAoF;YACpF,aAAa;QACjB;IACJ;IAEA,qDAAqD;IACrD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,kBAAkB;QAEvB,MAAM,eAAe;YACjB,IAAI;gBACA,MAAM,WAAW,qBAAqB,UAChC,MAAM,sHAAA,CAAA,aAAU,CAAC,mBAAmB,KACpC,MAAM,sHAAA,CAAA,aAAU,CAAC,UAAU;gBACjC,QAAQ,GAAG,CAAC,8BAA8B,SAAS,IAAI;gBACvD,WAAW,SAAS,IAAI;YAC5B,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,8BAA8B;YAChD;QACJ;QAEA;IACJ,GAAG;QAAC;KAAiB;IAErB,gEAAgE;IAChE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,0BAA0B;YAC5B,IAAI;gBACA,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,UAAU;gBAC5C,sBAAsB,SAAS,IAAI;YACvC,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,8BAA8B;YAChD;QACJ;QAEA;IACJ,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,WAAW,MAAM,EAAE;QAExB,MAAM,gBAAgB;YAClB;eACG,MAAM,IAAI,CAAC,IAAI,IAAI,WAAW,GAAG,CAAC,CAAA,MAAO,IAAI,MAAM;SACzD;QAED,iBAAiB,cAAc,MAAM,CAAC,CAAC,SAA6B,WAAW;IACnF,GAAG;QAAC;KAAW;IAEf,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,gBAAgB,MAAM,EAAE;QAE7B,MAAM,gBAAgB;YAClB;eACG,MAAM,IAAI,CAAC,IAAI,IAAI,gBAAgB,GAAG,CAAC,CAAA,MAAO,IAAI,MAAM;SAC9D;QAED,wBAAwB,cAAc,MAAM,CAAC,CAAC,SAA6B,WAAW;IAC1F,GAAG;QAAC;KAAgB;IAEpB,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,cAAc,OAAO,CAAC,kBAAkB;QAE7C,MAAM,YAAY;YACd,IAAI;gBACA,uBAAuB;gBACvB,MAAM,WAAW,qBAAqB,UAChC,MAAM,sHAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC,aAAa,GAAG,IAClD,MAAM,sHAAA,CAAA,aAAU,CAAC,WAAW,CAAC,aAAa,GAAG;gBACnD,4DAA4D;gBAC5D,IAAI,qBAAqB,SAAS;oBAC9B,cAAc,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,MAAa,CAAC;4BAC3C,OAAO,IAAI,UAAU;4BACrB,OAAO,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,UAAU,EAAE;4BAClD,QAAQ,IAAI,MAAM,IAAI;wBAC1B,CAAC;gBACL,OAAO;oBACH,cAAc,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,MAAa,CAAC;4BAC3C,OAAO,IAAI,EAAE;4BACb,OAAO,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE;4BAC1C,QAAQ,IAAI,MAAM,IAAI;wBAC1B,CAAC;gBACL;YAEJ,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,cAAc,EAAE;YACpB,SAAU;gBACN,iBAAiB,CAAA;oBACb,MAAM,YAAY;wBAAE,GAAG,IAAI;wBAAE,YAAY;oBAAK;oBAC9C,4DAA4D;oBAC5D,mBAAmB;oBACnB,OAAO;gBACX;gBACA,uBAAuB;YAC3B;QACJ;QAEA;IACJ,GAAG;QAAC;QAAc;KAAiB;IAEnC,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,mBAAmB,KAAK;QAE7B,MAAM,YAAY;YACd,IAAI;gBACA,wBAAwB;gBACxB,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,WAAW,CAAC,kBAAkB,GAAG;gBACnE,mBAAmB,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,MAAa,CAAC;wBAChD,OAAO,IAAI,EAAE;wBACb,OAAO,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE;wBAC1C,QAAQ,IAAI,MAAM,IAAI;oBAC1B,CAAC;YACL,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,mBAAmB,EAAE;YACzB,SAAU;gBACN,iBAAiB,CAAA;oBACb,MAAM,YAAY;wBAAE,GAAG,IAAI;wBAAE,iBAAiB;oBAAK;oBACnD,4DAA4D;oBAC5D,mBAAmB;oBACnB,OAAO;gBACX;gBACA,wBAAwB;YAC5B;QACJ;QAEA;IACJ,GAAG;QAAC;KAAkB;IAEtB,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,mBAAmB,KAAK;QAE7B,MAAM,aAAa;YACf,IAAI;gBACA,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,YAAY,CAAC,kBAAkB,GAAG;gBACpE,SAAS,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;wBACvC,OAAO,KAAK,EAAE;wBACd,OAAO,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;oBAChD,CAAC;YACL,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,SAAS,EAAE;YACf,SAAU;gBACN,iBAAiB,CAAA;oBACb,MAAM,YAAY;wBAAE,GAAG,IAAI;wBAAE,OAAO;oBAAK;oBACzC,4DAA4D;oBAC5D,mBAAmB;oBACnB,OAAO;gBACX;YACJ;QACJ;QAEA;IACJ,GAAG;QAAC;KAAkB;IAEtB,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,wBAAwB;YAC1B,IAAI;gBACA,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,mBAAmB;gBACrD,oBAAoB,SAAS,IAAI;YACrC,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,MAAM;oBACF,OAAO;oBACP,aAAa;oBACb,SAAS;gBACb;YACJ;QACJ;QAEA;IACJ,GAAG,EAAE;IAEL,oDAAoD;IACpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,mBAAmB,KAAK;YACzB,kBAAkB,EAAE;YACpB,SAAS,mBAAmB;YAC5B,SAAS,qBAAqB;YAC9B;QACJ;QAEA,MAAM,sBAAsB;YACxB,IAAI;gBACA,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC,kBAAkB,GAAG;gBACzE,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;oBACvB,kBAAkB,SAAS,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAwB,CAAC;4BAC/D,OAAO,KAAK,EAAE;4BACd,OAAO,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;wBAChD,CAAC;gBACL,OAAO;oBACH,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,OAAO,IAAI;gBAC7C;YACJ,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,MAAM;oBACF,OAAO;oBACP,aAAa;oBACb,SAAS;gBACb;gBACA,kBAAkB,EAAE;YACxB,SAAU;gBACN,iBAAiB,CAAA;oBACb,MAAM,YAAY;wBAAE,GAAG,IAAI;wBAAE,gBAAgB;oBAAK;oBAClD,4DAA4D;oBAC5D,mBAAmB;oBACnB,OAAO;gBACX;YACJ;QACJ;QAEA;IACJ,GAAG;QAAC;KAAkB;IAEtB,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,mBAAmB;YACrB,IAAI;gBACA,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,cAAc;gBAChD,eAAe,SAAS,IAAI;YAChC,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,MAAM;oBACF,OAAO;oBACP,aAAa;oBACb,SAAS;gBACb;YACJ;QACJ;QAEA;IACJ,GAAG,EAAE;IAEL,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,iBAAiB,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,cAAc;QACjE,IAAI,CAAC,gBAAgB,SAAS,KAAK;YAC/B,iBAAiB,EAAE;YACnB,SAAS,iBAAiB,EAAE;YAC5B;QACJ;QAEA,MAAM,qBAAqB;YACvB,IAAI;gBACA,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,sBAAsB,CAAC,eAAe,OAAO,CAAC,GAAG;gBACnF,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,EAAE;oBACxB,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,OAAO,IAAI;gBAC7C;gBAEA,MAAM,UAAU,SAAS,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAuB,CAAC;wBAC5D,OAAO,KAAK,GAAG;wBACf,OAAO,KAAK,KAAK;oBACrB,CAAC;gBACD,iBAAiB;YACrB,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,MAAM;oBACF,OAAO;oBACP,aAAa;oBACb,SAAS;gBACb;gBACA,iBAAiB,EAAE;YACvB;QACJ;QAEA;IACJ,GAAG;QAAC,cAAc;QAAK;KAAQ;IAE/B,2DAA2D;IAC3D,MAAM,iBAAiB,CAAC,SAAyB;QAC7C,4DAA4D;QAC5D,OAAO;YACH,UAAU,YAAY,QAAQ,GAAG,QAAQ,QAAQ,GAAG,QAAQ,QAAQ,GAAG,YAAY,QAAQ;YAC3F,QAAQ,YAAY,MAAM,GAAG,QAAQ,MAAM,GAAG,QAAQ,MAAM,GAAG,YAAY,MAAM;YACjF,SAAS,YAAY,OAAO,GAAG,QAAQ,OAAO,GAAG,QAAQ,OAAO,GAAG,YAAY,OAAO;YACtF,MAAM,YAAY,IAAI,GAAG,QAAQ,IAAI,GAAG,QAAQ,IAAI,GAAG,YAAY,IAAI;QAC3E;IACJ;IAEA,4BAA4B;IAC5B,MAAM,aAAa,CAAC;QAChB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,IAAI,KAAK,MAAM,cAAc,CAAC;IACzC;IAEA,mDAAmD;IACnD,MAAM,qBAAqB;QACvB,IAAI,CAAC,cAAc,iBAAiB,MAAM,KAAK,GAAG;YAC9C,MAAM;gBACF,OAAO;gBACP,aAAa;gBACb,SAAS;YACb;YACA;QACJ;QAEA,MAAM,cAAc;YAChB,UAAU;mBAAI;aAAiB,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;YACnD,MAAM;QACV;QAEA,mBAAmB;eAAI;YAAiB;SAAY;QACpD,oBAAoB,EAAE;QACtB,cAAc;IAClB;IAEA,mDAAmD;IACnD,MAAM,gBAAgB,CAAC;QACnB,oBAAoB,CAAA,OAChB,KAAK,QAAQ,CAAC,YACR,KAAK,MAAM,CAAC,CAAA,MAAO,QAAQ,YAC3B;mBAAI;gBAAM;aAAS;IAEjC;IAEA,2CAA2C;IAC3C,MAAM,qBAAqB,CAAC;QACxB,MAAM,QAAQ;YACV,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,eAAe;QACnB;QACA,OAAO,KAAK,CAAC,KAA2B,IAAI;IAChD;IAEA,2CAA2C;IAC3C,MAAM,6BAA6B,CAAC;QAChC,IAAI,CAAC,WAAW,OAAO;QAEvB,MAAM,MAAM,IAAI;QAChB,MAAM,QAAQ,OAAO,cAAc,WAAW,IAAI,KAAK,aAAa;QAEpE,8BAA8B;QAC9B,IAAI,MAAM,MAAM,OAAO,KAAK;YACxB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACX;QAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,MAAM,OAAO,EAAE,IAAI;QACrE,6FAA6F;QAE7F,2CAA2C;QAC3C,IAAI,iBAAiB,OAAO;YACxB,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;YACzC,OAAO,GAAG,MAAM,CAAC,CAAC;QACtB;QAEA,mDAAmD;QACnD,IAAI,iBAAiB,MAAM;YACvB,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;YACzC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,gBAAgB,OAAQ;YACpD,OAAO,GAAG,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;QAClC;QAEA,uDAAuD;QACvD,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAC3C,MAAM,UAAU,gBAAgB;QAEhC,IAAI,YAAY,GAAG;YACf,OAAO,GAAG,QAAQ,CAAC,CAAC;QACxB;QAEA,OAAO,GAAG,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC;IACpC;IAEA,wCAAwC;IACxC,MAAM,oBAAoB,OAAO;QAC7B,IAAI;YACA,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,eAAe,CAAC;YAClD,MAAM,OAAoB,SAAS,IAAI;YAEvC,qDAAqD;YACrD,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,GAAG;gBACnC,sBAAsB,CAAA,OAAQ,IAAI,IAAI,MAAM,GAAG,CAAC;gBAEhD,qCAAqC;gBACrC,mBAAmB,CAAA;oBACf,MAAM,gBAAgB,IAAI,CAAC,WAAW;oBACtC,OAAO;wBACH,GAAG,IAAI;wBACP,CAAC,WAAW,EAAE;4BACV,aAAa;4BACb,qEAAqE;4BACrE,oBAAoB,eAAe;wBACvC;oBACJ;gBACJ;gBAEA,gBAAgB,CAAA;oBACZ,MAAM,UAAU,YAAY;2BAAI;2BAAa,KAAK,IAAI;qBAAC;oBACvD,OAAO;gBACX;YACJ;YAEA,IAAI,KAAK,cAAc,EAAE;gBACrB,kBAAkB,CAAA,OAAQ,eAAe,MAAM,KAAK,cAAc;YACtE;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,wBAAwB;QAC1C;IACJ;IAEA,4DAA4D;IAC5D,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,MAAM,qBAAsD,CAAC;QAE7D,KAAK,MAAM,YAAY,UAAW;YAC9B,IAAI;gBACA,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,0BAA0B,CAAC,SAAS,GAAG;gBACzE,MAAM,OAAO,SAAS,IAAI;gBAE1B,wBAAwB;gBACxB,oBAAoB,CAAA,OAAQ,IAAI,IAAI;2BAAI;wBAAM,SAAS,GAAG;qBAAC;gBAE3D,IAAI,KAAK,WAAW,EAAE;oBAClB,sBAAsB,CAAA,OAAQ,IAAI,IAAI,MAAM,GAAG,CAAC,SAAS,GAAG;oBAC5D,kBAAkB,CAAC,SAAS,GAAG,CAAC,GAAG;wBAC/B,aAAa;wBACb,uDAAuD;wBACvD,oBAAoB,KAAK,kBAAkB,GAAG,IAAI,KAAK,KAAK,kBAAkB,IAAI;oBACtF;oBAEA,iBAAiB;oBACjB,IAAI,KAAK,kBAAkB,EAAE;oBACzB,qGAAqG;oBACzG;gBACJ,OAAO;oBACH,sBAAsB,CAAA;wBAClB,MAAM,SAAS,IAAI,IAAI;wBACvB,OAAO,MAAM,CAAC,SAAS,GAAG;wBAC1B,OAAO;oBACX;oBACA,kBAAkB,CAAC,SAAS,GAAG,CAAC,GAAG;wBAC/B,aAAa;wBACb,oBAAoB;oBACxB;gBACJ;YACJ,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,CAAC,0CAA0C,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE;gBAC5E,4CAA4C;gBAC5C,IAAI,eAAe,CAAC,SAAS,GAAG,CAAC,EAAE;oBAC/B,kBAAkB,CAAC,SAAS,GAAG,CAAC,GAAG,eAAe,CAAC,SAAS,GAAG,CAAC;gBACpE;YACJ;QACJ;QAEA,mBAAmB;IACvB,GAAG;QAAC;QAAW;KAAgB;IAE/B,kCAAkC;IAClC,MAAM,iBAAiB;QACnB,IAAI;YACA,WAAW;YACX,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,YAAY;YAC9C,aAAa,SAAS,IAAI;YAE1B,4EAA4E;YAC5E,MAAM;QACV,EAAE,OAAO,OAAO;YACZ,MAAM;gBACF,OAAO;gBACP,aAAa;gBACb,SAAS;YACb;QACJ,SAAU;YACN,WAAW;QACf;IACJ;IAEA,2CAA2C;IAC3C,MAAM,wBAAwB,OAAO;QACjC,IAAI;YACA,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,kBAAkB,CAAC;YACrD,OAAO,SAAS,IAAI,CAAC,gBAAgB;QACzC,EAAE,OAAO,OAAO;YACZ,MAAM;gBACF,OAAO;gBACP,aAAa;gBACb,SAAS;YACb;YACA,OAAO,EAAE;QACb;IACJ;IAEA,+CAA+C;IAC/C,MAAM,kBAAkB,OAAO;QAC3B,IAAI;YACA,kFAAkF;YAClF,sBAAsB,CAAA,OAAQ,IAAI,IAAI,MAAM,GAAG,CAAC;YAChD,mBAAmB,CAAA,OAAQ,CAAC;oBACxB,GAAG,IAAI;oBACP,CAAC,GAAG,EAAE;wBACF,aAAa;wBACb,oBAAoB,KAAK,wDAAwD;oBACrF;gBACJ,CAAC;YAED,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,eAAe,CAAC;YAClD,0CAA0C;YAE1C,MAAM;gBACF,OAAO;gBACP,aAAa;YACjB;YAEA,+BAA+B;YAC/B,MAAM;YAEN,0DAA0D;YAC1D,MAAM;QAEV,EAAE,OAAO,OAAY;YACjB,MAAM;gBACF,OAAO;gBACP,aAAa,MAAM,OAAO,IAAI;gBAC9B,SAAS;YACb;YAEA,8CAA8C;YAC9C,sBAAsB,CAAA;gBAClB,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,MAAM,CAAC;gBACd,OAAO;YACX;YACA,mBAAmB,CAAA,OAAQ,CAAC;oBACxB,GAAG,IAAI;oBACP,CAAC,GAAG,EAAE;wBACF,aAAa;wBACb,oBAAoB;oBACxB;gBACJ,CAAC;QACL;IACJ;IAEA,kCAAkC;IAClC,MAAM,iBAAiB;QACnB,IAAI,CAAC,kBAAkB;QAEvB,IAAI;YACA,MAAM,sHAAA,CAAA,aAAU,CAAC,cAAc,CAAC,iBAAiB,GAAG;YAEpD,MAAM;gBACF,OAAO;gBACP,aAAa;YACjB;YAEA,oBAAoB;YACpB;QACJ,EAAE,OAAO,OAAO;YACZ,MAAM;gBACF,OAAO;gBACP,aAAa;gBACb,SAAS;YACb;QACJ;IACJ;IAEA,gCAAgC;IAChC,MAAM,cAAc,OAAO;QACvB,oBAAoB;QACpB,qBAAqB;QACrB,MAAM,UAAU,MAAM,sBAAsB,SAAS,GAAG;QACxD,IAAI,SAAS;YACT,oBAAoB,CAAA,OAAQ,OAAO;oBAAE,GAAG,IAAI;oBAAE,kBAAkB;gBAAQ,IAAI;QAChF;IACJ;IAEA,MAAM,YAAY;QACd,SAAS,oBAAoB;QAC7B,SAAS,gBAAgB;YAAE,KAAK;YAAI,MAAM;QAAG;QAC7C,SAAS,iBAAiB;QAC1B,SAAS,mBAAmB;QAC5B,cAAc,EAAE;QAChB,SAAS,qBAAqB;YAAE,KAAK;YAAI,MAAM;QAAG;QAClD,SAAS,gBAAgB;QACzB,SAAS,kBAAkB;QAC3B,mBAAmB,EAAE;QACrB,SAAS,UAAU;QACnB,SAAS,YAAY;QACrB,SAAS,qBAAqB;YAAE,KAAK;YAAI,MAAM;QAAG;QAClD,SAAS,mBAAmB;QAC5B,SAAS,qBAAqB;QAC9B,SAAS,gBAAgB;QACzB,SAAS,gBAAgB;IAC7B;IAEA,6CAA6C;IAC7C,MAAM,cAAc,CAAC;QACjB,MAAM,gBAAgB,IAAI;QAE1B,KAAK,OAAO,CAAC,CAAA;YACT,MAAM,MAAM,IAAI,OAAO;YACvB,kEAAkE;YAClE,IAAI,cAAc,GAAG,CAAC,MAAM;gBACxB,MAAM,cAAc,cAAc,GAAG,CAAC;gBACtC,YAAY,SAAS,GAAG,IAAI,SAAS;gBACrC,IAAI,IAAI,QAAQ,KAAK,WAAW;oBAC5B,YAAY,QAAQ,GAAG,IAAI,QAAQ;gBACvC;YACJ,OAAO;gBACH,cAAc,GAAG,CAAC,KAAK;oBAAE,GAAG,GAAG;gBAAC;YACpC;QACJ;QAEA,OAAO,MAAM,IAAI,CAAC,cAAc,MAAM,IACjC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;IACvF;IAEA,qCAAqC;IACrC,MAAM,iBAAiB,CAAC;QACpB,oBAAoB;QACpB,kBAAkB;QAClB,kBAAkB,SAAS,GAAG;QAE9B,4BAA4B;QAC5B,MAAM,WAAW,YAAY;YACzB,kBAAkB,SAAS,GAAG;QAClC,GAAG,OAAO,6BAA6B;QAEvC,sBAAsB;IAC1B;IAEA,6CAA6C;IAC7C,MAAM,kBAAkB;QACpB,IAAI,oBAAoB;YACpB,cAAc;QAClB;QACA,kBAAkB;QAClB,gBAAgB,EAAE;QAClB,kBAAkB;YACd,UAAU;YACV,QAAQ;YACR,SAAS;YACT,MAAM;QACV;IACJ;IAEA,0DAA0D;IAC1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,WAAW,YAAY,sBAAsB,OAAO,6BAA6B;QACvF,OAAO,IAAM,cAAc;IAC/B,GAAG;QAAC;KAAqB;IAEzB,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN;IACJ,GAAG,EAAE;IAEL,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,wBAAwB;YAC1B,IAAI;gBACA,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,mBAAmB;gBACrD,oBAAoB,SAAS,IAAI;YACrC,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,MAAM;oBACF,OAAO;oBACP,aAAa;oBACb,SAAS;gBACb;YACJ;QACJ;QAEA;IACJ,GAAG,EAAE;IAEL,sCAAsC;IACtC,MAAM,iBAAiB,CAAC;QACpB,MAAM,WAAW;YACb;YAAW;YAAW;YAAS;YAAU;YAAU;YAAS;SAC/D;QACD,OAAO,KAAK,GAAG,CAAC,CAAA,MAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC;IAC/C;IAEA,8CAA8C;IAC9C,MAAM,oBAAoB,CAAC,OAAe,sBACtC,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;sCAAM;;;;;;sCACP,8OAAC;;gCAAM;gCAAM;;;;;;;;;;;;;8BAEjB,8OAAC;oBAAI,WAAU;8BACX,cAAA,8OAAC;wBACG,WAAU;wBACV,OAAO;4BAAE,OAAO,GAAG,MAAM,CAAC,CAAC;wBAAC;;;;;;;;;;;;;;;;;IAM5C,yCAAyC;IACzC,MAAM,2BAA2B,CAAC;QAC9B,MAAM,SAAS,OAAO,MAAM,CAAC;QAC7B,OAAO,KAAK,KAAK,CAAC,OAAO,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,KAAK,OAAO,MAAM;IAC/E;IAEA,qDAAqD;IACrD,MAAM,uBAAuB,OAAO;QAChC,IAAI;YACA,sDAAsD;YAEtD,4BAA4B;YAC5B,IAAI,SAAS,iBAAiB,EAAE;gBAC5B,uFAAuF;gBACvF,IAAI;oBACA,MAAM,oBAAoB,MAAM,sHAAA,CAAA,aAAU,CAAC,kBAAkB,CAAC,SAAS,iBAAiB,CAAC,GAAG;oBAC5F,sEAAsE;oBACtE,2BAA2B,kBAAkB,IAAI;gBACrD,EAAE,OAAO,OAAO;oBACZ,QAAQ,KAAK,CAAC,sCAAsC;oBACpD,MAAM;wBACF,OAAO;wBACP,aAAa;wBACb,SAAS;oBACb;gBACJ;YACJ;YAEA,2BAA2B;YAC3B,IAAI,SAAS,YAAY,EAAE;gBACvB,6EAA6E;gBAC7E,IAAI;oBACA,MAAM,eAAe,MAAM,sHAAA,CAAA,aAAU,CAAC,aAAa,CAAC,SAAS,YAAY;oBACzE,4DAA4D;oBAC5D,sBAAsB,aAAa,IAAI;gBAC3C,EAAE,OAAO,OAAO;oBACZ,QAAQ,KAAK,CAAC,qCAAqC;oBACnD,MAAM;wBACF,OAAO;wBACP,aAAa;wBACb,SAAS;oBACb;gBACJ;YACJ;YAEA,oCAAoC;YACpC,IAAI,SAAS,aAAa,EAAE,SAAS,GAAG;gBACpC,IAAI;oBACA,MAAM,wBAAwB,MAAM,sHAAA,CAAA,aAAU,CAAC,kBAAkB,CAAC,SAAS,aAAa;oBAExF,IAAI,sBAAsB,IAAI,CAAC,OAAO,EAAE;wBACpC,yBAAyB,sBAAsB,IAAI,CAAC,IAAI;oBACxD;;;6BAGK,GACT,OAAO;wBACH,QAAQ,KAAK,CAAC,iCAAiC,sBAAsB,IAAI,CAAC,OAAO;wBACjF,MAAM;4BACF,OAAO;4BACP,aAAa;4BACb,SAAS;wBACb;oBACJ;gBACJ,EAAE,OAAO,OAAO;oBACZ,QAAQ,KAAK,CAAC,iCAAiC;oBAC/C,MAAM;wBACF,OAAO;wBACP,aAAa;wBACb,SAAS;oBACb;gBACJ;YACJ,OAAO;gBACH,yBAAyB,EAAE;YAC/B;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM;gBACF,OAAO;gBACP,aAAa;gBACb,SAAS;YACb;QACJ;IACJ;IAEA,yCAAyC;IACzC,MAAM,oBAAoB,OAAO;QAC7B,QAAQ,GAAG,CAAC,+BAA+B;QAC3C,oBAAoB;QACpB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,sBAAsB;QACtB,yBAAyB,EAAE;QAC3B,qBAAqB;QACrB,MAAM,qBAAqB;IAC/B;IAEA,MAAM,iBAAiB,OAAO;QAC1B,aAAa,OAAO,2BAA2B;QAC/C,oBAAoB;QACpB,qDAAqD;QACrD,SAAS,QAAQ,SAAS,IAAI;QAC9B,SAAS,oBAAoB,SAAS,gBAAgB;QACtD,SAAS,gBAAgB,SAAS,YAAY;QAC9C,SAAS,iBAAiB,SAAS,aAAa;QAChD,SAAS,mBAAmB,SAAS,eAAe;QACpD,SAAS,qBAAqB,SAAS,iBAAiB;QACxD,SAAS,gBAAgB,SAAS,YAAY;QAC9C,SAAS,kBAAkB,SAAS,cAAc;QAClD,SAAS,UAAU,SAAS,MAAM;QAClC,SAAS,YAAY,SAAS,QAAQ;QACtC,SAAS,qBAAqB,SAAS,iBAAiB;QACxD,iEAAiE;QACjE,SAAS,mBAAmB,SAAS,eAAe;QACpD,SAAS,qBAAqB,SAAS,iBAAiB;QACxD,SAAS,gBAAgB,SAAS,YAAY;QAC9C,SAAS,gBAAgB,SAAS,YAAY;QAC9C,SAAS,YAAY,SAAS,QAAQ;QACtC,IAAI,SAAS,YAAY,KAAK,iBAAiB;YAC3C,SAAS,gBAAgB,SAAS,YAAY;QAClD,OAAO;YACH,mBAAmB,SAAS,cAAc,IAAI,EAAE;QACpD;QAEA,mCAAmC;QACnC,IAAI;YACA,wDAAwD;YACxD,4EAA4E;YAE5E,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,kBAAkB,CAAC,SAAS,aAAa;YAC3E,qDAAqD;YAErD,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACvB,mEAAmE;gBAEnE,MAAM,uBAAuB,SAAS,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;wBAChE,OAAO,KAAK,GAAG;wBACf,OAAO,KAAK,KAAK;oBACrB,CAAC;gBACD,yCAAyC;gBACzC,oEAAoE;gBAEpE,wCAAwC;gBACxC,qEAAqE;gBACrE,wBAAwB;gBACxB,uBAAuB;gBACvB,MAAM;gBACN,SAAS,iBAAiB;YAC1B,gFAAgF;YACpF,OAAO;gBACH,QAAQ,IAAI,CAAC,+BAA+B,SAAS,IAAI,CAAC,OAAO;gBACjE,MAAM;oBACF,OAAO;oBACP,aAAa,SAAS,IAAI,CAAC,OAAO,IAAI;oBACtC,SAAS;gBACb;YACJ;QAEJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM;gBACF,OAAO;gBACP,aAAa;gBACb,SAAS;YACb;QACJ;QACA,kBAAkB;IACtB;IAEA,+CAA+C;IAC/C;;;;;;;;;;;;;MAaE,GAEF,uEAAuE;IACvE;;;;;;;;;;;;;;;;;;;MAmBE,GAEF,mDAAmD;IACnD,MAAM,uBAAuB,CAAC;QAC1B,MAAM,mBAAmB;eAAI;SAAgB;QAC7C,iBAAiB,MAAM,CAAC,OAAO;QAC/B,mBAAmB;IACvB;IAEA,uCAAuC;IACvC,MAAM,WAAW,OAAO;QACpB,WAAW;QACX,IAAI;YACA,qBAAqB;YAErB,IAAI,CAAC,SAAS,IAAI,EAAE;gBAChB,MAAM,IAAI,MAAM;YACpB;YAEA,IAAI,CAAC,SAAS,gBAAgB,EAAE;gBAC5B,MAAM,IAAI,MAAM;YACpB;YAEA,IAAI,CAAC,SAAS,YAAY,IAAI,CAAC,SAAS,aAAa,IAAI,CAAC,SAAS,eAAe,EAAE;gBAChF,MAAM,IAAI,MAAM;YACpB;YAEA,IAAI,CAAC,SAAS,iBAAiB,IAAI,CAAC,SAAS,YAAY,IAAI,CAAC,SAAS,cAAc,EAAE;gBACnF,MAAM,IAAI,MAAM;YACpB;YAEA,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,SAAS,QAAQ,EAAE;gBACxC,MAAM,IAAI,MAAM;YACpB;YAEA,IAAI,CAAC,SAAS,iBAAiB,CAAC,GAAG,IAAI,CAAC,SAAS,eAAe,IAAI,CAAC,SAAS,iBAAiB,EAAE;gBAC7F,MAAM,IAAI,MAAM;YACpB;YAEA,IAAI,CAAC,SAAS,YAAY,EAAE;gBACxB,MAAM,IAAI,MAAM;YACpB;YAEA,IAAI,CAAC,SAAS,aAAa,IAAI,SAAS,aAAa,CAAC,MAAM,KAAK,GAAG;gBAChE,MAAM,IAAI,MAAM;YACpB;YAEA,IAAI,CAAC,SAAS,YAAY,EAAE;gBACxB,MAAM,IAAI,MAAM;YACpB;YAEA,qDAAqD;YACrD,IAAI,SAAS,YAAY,KAAK,mBAAmB,CAAC,SAAS,YAAY,EAAE;gBACrE,MAAM,IAAI,MAAM;YACpB;YAEA,IAAI,SAAS,YAAY,KAAK,mBAAmB,CAAC,CAAC,mBAAmB,gBAAgB,MAAM,KAAK,CAAC,GAAG;gBACjG,MAAM,IAAI,MAAM;YACpB;YAEA,uBAAuB;YACvB,MAAM,cAAc;gBAChB,MAAM,SAAS,IAAI;gBACnB,kBAAkB,SAAS,gBAAgB;gBAC3C,cAAc;oBACV,KAAK,SAAS,YAAY,CAAC,GAAG;oBAC9B,MAAM,SAAS,YAAY,CAAC,IAAI;gBACpC;gBACA,eAAe,SAAS,aAAa;gBACrC,iBAAiB,SAAS,eAAe;gBACzC,mBAAmB;oBACf,KAAK,SAAS,iBAAiB,CAAC,GAAG;oBACnC,MAAM,SAAS,iBAAiB,CAAC,IAAI;gBACzC;gBACA,cAAc,SAAS,YAAY;gBACnC,gBAAgB,SAAS,cAAc;gBACvC,QAAQ,SAAS,MAAM;gBACvB,UAAU,SAAS,QAAQ;gBAC3B,mBAAmB,SAAS,iBAAiB,CAAC,GAAG;gBACjD,iBAAiB,SAAS,eAAe;gBACzC,mBAAmB,SAAS,iBAAiB;gBAC7C,cAAc,SAAS,YAAY;gBACnC,eAAe,SAAS,aAAa,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;gBAC5D,cAAc,SAAS,YAAY;gBACnC,cAAc,SAAS,YAAY,KAAK,kBAAkB,SAAS,YAAY,GAAG;gBAClF,gBAAgB,SAAS,YAAY,KAAK,kBAAkB,gBAAgB,GAAG,CAAC,CAAA,WAAY,CAAC;wBACzF,UAAU,SAAS,QAAQ;wBAC3B,MAAM,SAAS,IAAI;oBACvB,CAAC,KAAK;gBACN,UAAU,SAAS,QAAQ;YAC/B;YAEA,2EAA2E;YAE3E,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,cAAc,CAAC,kBAAkB,KAAM;YAEzE,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACvB,MAAM;oBACF,OAAO;oBACP,aAAa;gBACjB;gBACA,kBAAkB;gBAClB,kBAAkB,mBAAmB;gBACrC,iCAAiC;gBACjC;YACJ,OAAO;gBACH,MAAM;oBACF,OAAO;oBACP,aAAa,SAAS,IAAI,CAAC,OAAO,IAAI;oBACtC,SAAS;gBACb;YACJ;QAGJ,EAAE,OAAO,OAAY;YACjB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;YACvE,MAAM;gBACF,OAAO;gBACP,aAAa;gBACb,SAAS;YACb;QACJ,SAAU;YACN,WAAW;QACf;IACJ;IAEA,MAAM,qBAAqB,CAAC;QACxB,qBAAqB;IACzB;IAEA,MAAM,yBAAyB;QAC3B,IAAI,mBAAmB;YACnB,qBAAqB,OAAO,kCAAkC;YAC9D,MAAM,gBAAgB;QAC1B;IACJ;IAIA,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACZ,8OAAC,gIAAA,CAAA,aAAU;kCACP,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEf,8OAAC,gIAAA,CAAA,cAAW;kCACP,wBACG,8OAAC;4BAAI,WAAU;sCACX,cAAA,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;iDAGvB,8OAAC,iIAAA,CAAA,QAAK;;8CACF,8OAAC,iIAAA,CAAA,cAAW;8CACR,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;0DAKL,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;;;;;;;8CAGnB,8OAAC,iIAAA,CAAA,YAAS;8CACL,UAAU,GAAG,CAAC,CAAC,yBACZ,8OAAC,iIAAA,CAAA,WAAQ;;8DAeL,8OAAC,iIAAA,CAAA,YAAS;8DAAE,SAAS,IAAI;;;;;;8DACzB,8OAAC,iIAAA,CAAA,YAAS;8DAAE,mBAAmB,SAAS,YAAY;;;;;;8DACpD,8OAAC,iIAAA,CAAA,YAAS;8DAAE,WAAW,SAAS,aAAa;;;;;;8DAC7C,8OAAC,iIAAA,CAAA,YAAS;8DAAE,WAAW,SAAS,aAAa;;;;;;8DAC7C,8OAAC,iIAAA,CAAA,YAAS;8DACN,cAAA,8OAAC;wDAAI,WAAU;;0EACX,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAS,SAAS,QAAQ,GAAG,YAAY;gEAAa,WAAU;0EAClE,SAAS,QAAQ,GAAG,UAAU;;;;;;4DAElC,eAAe,CAAC,SAAS,GAAG,CAAC,EAAE,6BAC5B,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;;oEAAgD;oEAC/D,2BAA2B,eAAe,CAAC,SAAS,GAAG,CAAC,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;8DAK3G,8OAAC,iIAAA,CAAA,YAAS;8DACN,cAAA,8OAAC;wDAAI,WAAU;;0EACX,8OAAC,kIAAA,CAAA,SAAM;gEACH,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,YAAY;gEAC3B,OAAM;0EAEN,cAAA,8OAAC,wMAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;;;;;;0EAEvB,8OAAC,kIAAA,CAAA,SAAM;gEACH,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,eAAe;gEAC9B,OAAM;gEACN,UAAU,eAAe,CAAC,SAAS,GAAG,CAAC,EAAE;0EAEzC,cAAA,8OAAC,2MAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAEpB,8OAAC,kIAAA,CAAA,SAAM;gEACH,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,mBAAmB,SAAS,GAAG;gEAC9C,UAAU,CAAC,iBAAiB,GAAG,CAAC,SAAS,GAAG,KAAK,eAAe,CAAC,SAAS,GAAG,CAAC,EAAE;gEAChF,OAAM;0EAEL,CAAC,iBAAiB,GAAG,CAAC,SAAS,GAAG,KAAK,eAAe,CAAC,SAAS,GAAG,CAAC,EAAE,4BACnE,8OAAC,iNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;yFAEnB,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;4DAGvB,eAAe,CAAC,SAAS,GAAG,CAAC,EAAE,6BAC5B,8OAAC,kIAAA,CAAA,SAAM;gEACH,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,eAAe;gEAC9B,OAAM;0EAEN,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;0EAG5B,8OAAC,kIAAA,CAAA,SAAM;gEACH,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,kBAAkB;gEACjC,OAAM;0EAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAEpB,8OAAC,kIAAA,CAAA,SAAM;gEACH,SAAQ;gEACR,MAAK;gEACL,SAAS;oEACL,oBAAoB;oEACpB,oBAAoB;gEACxB;gEACA,OAAM;gEACN,UAAU,eAAe,CAAC,SAAS,GAAG,CAAC,EAAE;0EAEzC,cAAA,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CA3FnB,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAwGnD,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAmB,cAAc;0BAC3C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACrB,8OAAC,kIAAA,CAAA,eAAY;;8CACT,8OAAC,kIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;wBAItB,kBAAkB,kBAAkB,WAAW,kBAC5C,8OAAC;4BAAI,WAAU;sCAAyC;;;;;mCAIxD;+BAAK,kBAAkB,oBAAoB,EAAE;yBAAE,CAC1C,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,IAC9E,GAAG,CAAC,CAAC,WAAW,sBACb,8OAAC,gIAAA,CAAA,OAAI;gCAAa,WAAU;;kDACxB,8OAAC,gIAAA,CAAA,aAAU;kDACP,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACjB,8OAAC;oDAAK,WAAU;;sEACZ,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAAY;wDAChB,WAAW,UAAU,SAAS;;;;;;;8DAE/C,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAS,UAAU,OAAO,GAAG,YAAY;8DAC3C,UAAU,OAAO,GAAG,YAAY;;;;;;;;;;;;;;;;;kDAI7C,8OAAC,gIAAA,CAAA,cAAW;kDACR,cAAA,8OAAC;4CAAI,WAAU;;gDACV,UAAU,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACxB,8OAAC;wDAAoB,WAAU;;0EAC3B,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAS,KAAK,MAAM,KAAK,cAAc,YAAY;gEAAa,WAAU;;oEAAmC;oEACzG,KAAK,IAAI;;;;;;;0EAEpB,8OAAC;0EAAM,KAAK,OAAO;;;;;;;uDAJb;;;;;gDAOb,UAAU,KAAK,kBACZ,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,8OAAC;sEAAG,UAAU,KAAK,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;+BAzBpC;;;;;;;;;;;;;;;;0BAqC/B,8OAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BAC/C,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACf,8OAAC,2IAAA,CAAA,oBAAiB;;8CACd,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAI5B,8OAAC,2IAAA,CAAA,oBAAiB;;8CACd,8OAAC,2IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,8OAAC,2IAAA,CAAA,oBAAiB;oCAAC,SAAS;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAQxD,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAgB,cAAc;0BACxC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACrB,8OAAC,kIAAA,CAAA,eAAY;;8CACT,8OAAC,kIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,8OAAC,kIAAA,CAAA,oBAAiB;8CACb,oBAAoB,eAAe,CAAC,iBAAiB,GAAG,CAAC,EAAE,oCACxD,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;;oDAAK;oDACoB,2BAA2B,eAAe,CAAC,iBAAiB,GAAG,CAAC,CAAC,kBAAkB;;;;;;;0DAE7G,8OAAC;gDAAI,WAAU;;oDAEV,kBAAkB,yBAAyB,iBAAiB;kEAG7D,8OAAC;wDAAI,WAAU;;4DACV,kBAAkB,eAAe,QAAQ,EAAE;4DAC3C,kBAAkB,eAAe,MAAM,EAAE;4DACzC,kBAAkB,eAAe,OAAO,EAAE;4DAC1C,kBAAkB,eAAe,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhE,8OAAC;4BAAI,WAAU;sCACV,aAAa,MAAM,KAAK,kBACrB,8OAAC;gCAAI,WAAU;0CAAgB;;;;;qDAE/B,8OAAC;gCAAI,WAAU;0CACV,aAAa,MAAM,CAAC,CAAC,KAAoB,KAAK;oCAC3C,sDAAsD;oCACtD,MAAM,cAAc,IAAI,OAAO,CAAC,OAAO,CAAC,WAAW;oCAEnD,6CAA6C;oCAC7C,MAAM,sBAAsB,IAAI,SAAS,CACrC,CAAC,OAAS,KAAK,GAAG,IAClB,YAAY,CAAC,SAAS,KAAK,GAAG,EAAY,EAAE,QAAQ,QAAQ,WAAW,QAAQ;oCAGnF,8DAA8D;oCAC9D,IAAI,wBAAwB,CAAC,KAAK,IAAI,QAAQ,KAAK,WAAW;wCAC1D,GAAG,CAAC,oBAAoB,iBACpB,8OAAC;4CAAgB,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;;wDAAkC;wDAC5C,IAAI,KAAK,IAAI,SAAS,EAAE,kBAAkB;wDAAG;;;;;;;8DAEnD,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;sEAAM,IAAI,OAAO;;;;;;wDACjB,IAAI,QAAQ,KAAK,2BACd,8OAAC;4DAAI,WAAU;sEACX,cAAA,8OAAC;gEACG,WAAU;gEACV,OAAO;oEAAE,OAAO,GAAG,IAAI,QAAQ,CAAC,CAAC,CAAC;gEAAC;;;;;;;;;;;;;;;;;;2CAV7C;;;;;oCAiBlB,OAEK,IAAI,wBAAwB,CAAC,GAAG;wCACjC,IAAI,IAAI,eACJ,8OAAC;4CAAgB,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;;wDAAkC;wDAC5C,IAAI,KAAK,IAAI,SAAS,EAAE,kBAAkB;wDAAG;;;;;;;8DAEnD,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;sEAAM,IAAI,OAAO;;;;;;wDACjB,IAAI,QAAQ,KAAK,2BACd,8OAAC;4DAAI,WAAU;sEACX,cAAA,8OAAC;gEACG,WAAU;gEACV,OAAO;oEAAE,OAAO,GAAG,IAAI,QAAQ,CAAC,CAAC,CAAC;gEAAC;;;;;;;;;;;;;;;;;;2CAV7C;;;;;oCAiBlB;oCACA,OAAO;gCACX,GAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;0BAQzB,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAmB,cAAc;0BAC3C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACrB,8OAAC,kIAAA,CAAA,eAAY;;8CACT,8OAAC,kIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,8OAAC,kIAAA,CAAA,oBAAiB;8CACb,kBAAkB;;;;;;;;;;;;wBAG1B,kCACG,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;;8DACG,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAE,WAAU;8DAAW,iBAAiB,gBAAgB,KAAK,QAAQ,yCAAyC;;;;;;;;;;;;sDAEnH,8OAAC;;8DACG,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAE,WAAU;8DAAW,iBAAiB,YAAY,CAAC,IAAI;;;;;;;;;;;;sDAE9D,8OAAC;;8DACG,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;;gEAAE;gEAAK,iBAAiB,aAAa,IAAI;;;;;;;sEAC1C,8OAAC;;gEAAE;gEAAO,iBAAiB,eAAe,IAAI;;;;;;;;;;;;;;;;;;;sDAGtD,8OAAC;;8DACG,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAE,WAAU;8DAAW,iBAAiB,iBAAiB,CAAC,IAAI;;;;;;;;;;;;sDAEnE,8OAAC;;8DACG,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;;gEAAE;gEAAK,iBAAiB,YAAY,IAAI;;;;;;;sEACzC,8OAAC;;gEAAE;gEAAO,iBAAiB,cAAc,IAAI;;;;;;;;;;;;;;;;;;;sDAGrD,8OAAC;;8DACG,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;;gEAAE;gEAAK,iBAAiB,MAAM,IAAI;;;;;;;sEACnC,8OAAC;;gEAAE;gEAAO,iBAAiB,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;sDAG/C,8OAAC;;8DACG,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAE,WAAU;8DACR,0BACG,wBAAwB,IAAI,GAE5B,kBAAkB,oBAAoB,kBAAkB;;;;;;;;;;;;sDAIpE,8OAAC;;8DACG,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;gDAClC,mCACG,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;;gEAAE;gEAAU,mBAAmB,OAAO,CAAC,IAAI;;;;;;;sEAC5C,8OAAC;;gEAAE;gEAAY,mBAAmB,IAAI,CAAC,IAAI;;;;;;;sEAC3C,8OAAC;;gEAAE;gEAAS,mBAAmB,IAAI,CAAC,KAAK;;;;;;;;;;;;yEAG7C,8OAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;;;;;;;8CAInC,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;;8DACG,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;;gEAAE;gEAAK,iBAAiB,eAAe,IAAI;;;;;;;sEAC5C,8OAAC;;gEAAE;gEAAO,iBAAiB,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;;sDAGxD,8OAAC;;8DACG,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAE,WAAU;8DAAW,mBAAmB,iBAAiB,YAAY;;;;;;gDACvE,iBAAiB,YAAY,KAAK,gCAC/B,8OAAC;oDAAE,WAAU;;wDAAgC;wDAAU,iBAAiB,YAAY;;;;;;yEAEpF,8OAAC;oDAAI,WAAU;8DACV,iBAAiB,cAAc,EAAE,IAAI,CAAC,UAAU,sBAC7C,8OAAC;4DAAc,WAAU;;gEACpB,eAAe,SAAS,QAAQ;gEAAE;gEAAK,SAAS,IAAI;;2DADjD;;;;;;;;;;;;;;;;sDAOxB,8OAAC;;8DACG,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAI,WAAU;8DACV,sBAAsB,MAAM,GAAG,IAC5B,sBAAsB,GAAG,CAAC,CAAC,6BACvB,8OAAC;4DAA2B,WAAU;;8EAClC,8OAAC;;wEAAE;wEAAO,aAAa,IAAI;;;;;;;8EAC3B,8OAAC;oEAAE,WAAU;;wEAAwB;wEAAS,aAAa,KAAK;;;;;;;;2DAF1D,aAAa,GAAG;;;;kFAM9B,8OAAC;wDAAE,WAAU;kEAAU;;;;;;;;;;;;;;;;;sDAInC,8OAAC;;8DACG,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAI,WAAU;;sEACX,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAS,iBAAiB,QAAQ,GAAG,YAAY;sEACnD,iBAAiB,QAAQ,GAAG,UAAU;;;;;;wDAE1C,eAAe,CAAC,iBAAiB,GAAG,CAAC,EAAE,6BACpC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAAqB;;;;;;;;;;;;;;;;;;sDAMpE,8OAAC;;8DACG,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAE,WAAU;;wDAAU;wDAAS,WAAW,iBAAiB,aAAa;;;;;;;8DACzE,8OAAC;oDAAE,WAAU;;wDAAU;wDAAU,WAAW,iBAAiB,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlG,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAgB,cAAc;0BACxC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACrB,8OAAC,kIAAA,CAAA,eAAY;;8CACT,8OAAC,kIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;wBAKnB,kCACI,8OAAC;4BAAK,UAAU,aAAa;4BAAW,WAAU;;gCAE7C,2BACG,8OAAC;oCAAI,WAAU;oCAAmF,OAAO;wCAAE,QAAQ;oCAAG;8CAClH,cAAA,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAI,WAAU;0DACX,cAAA,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAOnC,8OAAC;;sDACG,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,8JAAA,CAAA,aAAU;4CACP,MAAK;4CACL,SAAS;4CACT,cAAc,kBAAkB;4CAChC,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACd,8OAAC,iIAAA,CAAA,QAAK;oDACF,aAAY;oDACX,GAAG,KAAK;;;;;;;;;;;;;;;;;8CAOzB,8OAAC;;sDACG,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,8JAAA,CAAA,aAAU;4CACP,MAAK;4CACL,SAAS;4CACT,cAAc,kBAAkB;4CAChC,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACd,8OAAC,kIAAA,CAAA,SAAM;oDACH,OAAO,MAAM,KAAK;oDAClB,eAAe,CAAC;wDACZ,MAAM,QAAQ,CAAC;wDACf,SAAS,gBAAgB;4DAAE,KAAK;4DAAI,MAAM;wDAAG;wDAC7C,SAAS,iBAAiB;wDAC1B,SAAS,mBAAmB;wDAC5B,cAAc,EAAE;oDACpB;;sEAEA,8OAAC,kIAAA,CAAA,gBAAa;sEACV,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE7B,8OAAC,kIAAA,CAAA,gBAAa;;8EACV,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAM;;;;;;8EACxB,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ9C,8OAAC;;sDACG,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,8JAAA,CAAA,aAAU;4CACP,MAAK;4CACL,SAAS;4CACT,cAAc,kBAAkB;4CAChC,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACd,8OAAC,kIAAA,CAAA,SAAM;oDACH,OAAO,MAAM,KAAK,EAAE,OAAO;oDAC3B,UAAU,CAAC;oDACX,eAAe,CAAC;wDACZ,MAAM,QAAQ,CAAC;4DAAE,KAAK;4DAAO,MAAM,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,QAAQ,QAAQ;wDAAG;wDAClF,cAAc,EAAE;wDAChB,SAAS,iBAAiB;wDAC1B,SAAS,mBAAmB;oDAChC;;sEAEA,8OAAC,kIAAA,CAAA,gBAAa;sEACV,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE7B,8OAAC,kIAAA,CAAA,gBAAa;sEACT,QAAQ,GAAG,CAAC,CAAC,uBACV,8OAAC,kIAAA,CAAA,aAAU;oEAAkB,OAAO,OAAO,GAAG;8EACzC,OAAO,IAAI;mEADC,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAWnD,8OAAC;oCAAI,WAAU;;sDACX,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,kIAAA,CAAA,SAAM;4CACH,eAAe;4CACf,OAAO;4CACP,UAAU,CAAC,cAAc;;8DAEzB,8OAAC,kIAAA,CAAA,gBAAa;8DACV,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE7B,8OAAC,kIAAA,CAAA,gBAAa;8DACT,oCACG,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;wDAAU,QAAQ;wDAAC,WAAU;kEAAyC;;;;;+DAGxF,cAAc,MAAM,IAAI,kBACxB,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAQ;;;;;+DAE1B,cAAc,GAAG,CAAC,CAAA,uBACd,8OAAC,kIAAA,CAAA,aAAU;4DAAc,OAAO;sEAC3B;2DADY;;;;;;;;;;;;;;;;;;;;;;8CAUrC,8OAAC;;sDACG,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,8JAAA,CAAA,aAAU;4CACP,MAAK;4CACL,SAAS;4CACT,cAAc,kBAAkB;4CAChC,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACd,8OAAC,mMAAA,CAAA,UAAW;oDACR,OAAO,WAAW,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK,MAAM,KAAK,KAAK;oDAClE,YAAW;oDACX,YAAY;oDACZ,gBAAgB,WAAW,MAAM,CAAC,CAAA,MAC9B,mBAAmB,WAAW,IAAI,MAAM,KAAK;oDAEjD,aAAa,CAAC,YAAoB;wDAC9B,MAAM,kBAAkB,WAAW,MAAM,CAAC,CAAC,SACvC,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;wDAE9D,SAAS;oDACb;oDACA,YAAY,CAAC,cAAc;oDAC3B,UAAU,CAAC;wDACP,MAAM,QAAQ,CAAC,QAAQ;wDACvB,MAAM,OAAO,QAAQ,MAAM,MAAM,SAAS,CAAC,EAAE,CAAC,QAAQ,UAAU,OAAO;wDACvE,SAAS,mBAAmB;oDAChC;oDACA,aAAY;oDACZ,iCAAiC,GACjC,WAAW;oDACX,gBAAgB,IAAM;oDACtB,kBAAkB,IAAM;;;;;;;;;;;;;;;;;8CAOxC,8OAAC;;sDACG,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,8JAAA,CAAA,aAAU;4CACP,MAAK;4CACL,SAAS;4CACT,cAAc,kBAAkB;4CAChC,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACd,8OAAC,kIAAA,CAAA,SAAM;oDACH,OAAO,MAAM,KAAK,EAAE,OAAO;oDAC3B,eAAe,CAAC;wDACZ,MAAM,QAAQ,CAAC;4DAAE,KAAK;4DAAO,MAAM,mBAAmB,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,QAAQ,QAAQ;wDAAG;wDAC7F,mBAAmB,EAAE;wDACrB,SAAS,EAAE;wDACX,SAAS,gBAAgB;wDACzB,SAAS,kBAAkB;wDAC3B,SAAS,UAAU;wDACnB,SAAS,YAAY;oDACzB;;sEAEA,8OAAC,kIAAA,CAAA,gBAAa;sEACV,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE7B,8OAAC,kIAAA,CAAA,gBAAa;sEACT,mBAAmB,GAAG,CAAC,CAAC,uBACrB,8OAAC,kIAAA,CAAA,aAAU;oEAAkB,OAAO,OAAO,GAAG;8EACzC,OAAO,IAAI;mEADC,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAWnD,8OAAC;oCAAI,WAAU;;sDACX,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,kIAAA,CAAA,SAAM;4CACH,eAAe;4CACf,OAAO;4CACP,UAAU,CAAC,mBAAmB;;8DAE9B,8OAAC,kIAAA,CAAA,gBAAa;8DACV,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE7B,8OAAC,kIAAA,CAAA,gBAAa;8DACT,qCACG,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;wDAAU,QAAQ;wDAAC,WAAU;kEAAyC;;;;;+DAGxF,qBAAqB,MAAM,IAAI,kBAC/B,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAQ;;;;;+DAE1B,qBAAqB,GAAG,CAAC,CAAA,uBACrB,8OAAC,kIAAA,CAAA,aAAU;4DAAc,OAAO;sEAAS;2DAAxB;;;;;;;;;;;;;;;;;;;;;;8CAQrC,8OAAC;;sDACG,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,8JAAA,CAAA,aAAU;4CACP,MAAK;4CACL,cAAc,kBAAkB;4CAChC,SAAS;4CACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACd,8OAAC,mMAAA,CAAA,UAAW;oDACR,OAAO,gBAAgB,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK,MAAM,KAAK,KAAK;oDACvE,YAAW;oDACX,YAAY;oDACZ,gBAAgB,gBAAgB,MAAM,CAAC,CAAA,MACnC,0BAA0B,WAAW,IAAI,MAAM,KAAK;oDAExD,aAAa,CAAC,YAAoB;wDAC9B,MAAM,kBAAkB,gBAAgB,MAAM,CAAC,CAAC,SAC5C,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;wDAE9D,SAAS;oDACb;oDACA,YAAY,CAAC,mBAAmB;oDAChC,UAAU,CAAC;wDACP,MAAM,QAAQ,CAAC,QAAQ;wDACvB,MAAM,OAAO,QAAQ,MAAM,MAAM,SAAS,CAAC,EAAE,CAAC,QAAQ,UAAU,OAAO;wDACvE,SAAS,kBAAkB;oDAC3B,+DAA+D;oDACnE;oDACA,aAAY;oDACZ,iCAAiC,GACjC,WAAW;oDACX,gBAAgB,IAAM;oDACtB,kBAAkB,IAAM;;;;;;;;;;;;;;;;;8CAOxC,8OAAC;;sDACG,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,8JAAA,CAAA,aAAU;4CACP,cAAc,kBAAkB;4CAChC,MAAK;4CACL,SAAS;4CACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACd,8OAAC,mMAAA,CAAA,UAAW;oDACR,OAAO,MAAM,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK,MAAM,KAAK,KAAK;oDAC7D,YAAW;oDACX,YAAY;oDACZ,gBAAgB;oDAChB,aAAa,CAAC,YAAoB;wDAC9B,MAAM,kBAAkB,MAAM,MAAM,CAAC,CAAC,SAClC,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;wDAE9D,SAAS;oDACb;oDACA,YAAY,CAAC,mBAAmB;oDAChC,UAAU,CAAC;wDACP,MAAM,QAAQ,CAAC,QAAQ;wDACvB,MAAM,OAAO,QAAQ,MAAM,MAAM,SAAS,CAAC,EAAE,CAAC,QAAQ,UAAU,OAAO;wDACvE,SAAS,YAAY;oDACzB;oDACA,aAAY;;;;;;;;;;;;;;;;;8CAO5B,8OAAC;;sDACG,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,8JAAA,CAAA,aAAU;4CACP,MAAK;4CACL,cAAc,kBAAkB;4CAChC,SAAS;4CACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACd,8OAAC,kIAAA,CAAA,SAAM;oDACH,4BAA4B;oDAC5B,2BAA2B;oDAC3B,OAAO,MAAM,KAAK,EAAE,OAAO;oDAC3B,eAAe,CAAC;wDACZ,MAAM,QAAQ,CAAC;wDACf,kBAAkB,EAAE;wDACpB,SAAS,mBAAmB;wDAC5B,SAAS,qBAAqB;oDAClC;;sEAEA,8OAAC,kIAAA,CAAA,gBAAa;sEACV,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE7B,8OAAC,kIAAA,CAAA,gBAAa;sEACT,iBAAiB,GAAG,CAAC,CAAC,uBACnB,8OAAC,kIAAA,CAAA,aAAU;oEAAkB,OAAO,OAAO,GAAG;8EACzC,OAAO,IAAI;mEADC,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAWnD,8OAAC;;sDACG,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,8JAAA,CAAA,aAAU;4CACP,MAAK;4CACL,cAAc,kBAAkB;4CAChC,SAAS;4CACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACd,8OAAC,mMAAA,CAAA,UAAW;oDACR,OAAO,eAAe,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK,MAAM,KAAK,KAAK;oDACtE,YAAW;oDACX,YAAY;oDACZ,gBAAgB;oDAChB,aAAa,CAAC,YAAoB;wDAC9B,MAAM,kBAAkB,eAAe,MAAM,CAAC,CAAC,SAC3C,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;wDAE9D,SAAS;oDACb;oDACA,YAAY,CAAC,mBAAmB;oDAChC,UAAU,CAAC;wDACP,MAAM,QAAQ,CAAC,QAAQ;wDACvB,MAAM,OAAO,QAAQ,MAAM,MAAM,SAAS,CAAC,EAAE,CAAC,QAAQ,UAAU,OAAO;wDACvE,SAAS,qBAAqB;oDAClC;oDACA,aAAY;;;;;;;;;;;;;;;;;8CAO5B,8OAAC;;sDACG,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,8JAAA,CAAA,aAAU;4CACP,cAAc,kBAAkB;4CAChC,MAAK;4CACL,SAAS;4CACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACd,8OAAC,kIAAA,CAAA,SAAM;oDACH,OAAO,MAAM,KAAK,IAAI;oDACtB,eAAe,MAAM,QAAQ;;sEAE7B,8OAAC,kIAAA,CAAA,gBAAa;sEACV,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE7B,8OAAC,kIAAA,CAAA,gBAAa;sEACT,YAAY,GAAG,CAAC,CAAC,uBACd,8OAAC,kIAAA,CAAA,aAAU;oEAAkB,OAAO,OAAO,GAAG;;wEAAE;wEAClC,OAAO,OAAO,CAAC,IAAI;wEAAC;wEAAU,OAAO,IAAI,CAAC,KAAK;;mEAD5C,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAWnD,8OAAC;;sDACG,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,8JAAA,CAAA,aAAU;4CACP,MAAK;4CACL,SAAS;4CACT,cAAc,EAAE;4CAChB,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACd,8OAAC,mMAAA,CAAA,UAAW;oDACR,YAAY,CAAC,qBAAqB,EAAE,kBAAkB,KAAK;oDAC3D,OAAO;oDACP,YAAY;oDACZ,OAAO,MAAM,KAAK,IAAI,EAAE;oDACxB,gBAAgB;oDAChB,aAAa,CAAC,YAAoB;wDAC9B,MAAM,kBAAkB,cAAc,MAAM,CAAC,CAAC,SAC1C,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;wDAE9D,SAAS;oDACb;oDACA,YAAY,CAAC,cAAc;oDAC3B,UAAU,CAAC;wDACP,MAAM,QAAQ,CAAC,mBAAmB,EAAE;oDACxC;oDACA,aAAY;;;;;;;;;;;;;;;;;8CAO5B,8OAAC;;sDACG,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,8JAAA,CAAA,aAAU;4CACP,MAAK;4CACL,SAAS;4CACT,cAAc,kBAAkB;4CAChC,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACd,8OAAC,kIAAA,CAAA,SAAM;oDACH,OAAO,MAAM,KAAK,IAAI;oDACtB,eAAe,MAAM,QAAQ;;sEAE7B,8OAAC,kIAAA,CAAA,gBAAa;sEACV,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE7B,8OAAC,kIAAA,CAAA,gBAAa;;8EACV,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;8EAC3B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAU;;;;;;8EAC5B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;8EAC3B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQrD,iBAAiB,iCACd,8OAAC;;sDACG,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,8JAAA,CAAA,aAAU;4CACP,MAAK;4CACL,SAAS;4CACT,cAAa;4CACb,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACd,8OAAC,iIAAA,CAAA,QAAK;oDAAC,MAAK;oDAAQ,GAAG,KAAK;oDAAE,OAAO,MAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;gCAOnE,iBAAiB,iCACd,8OAAC;oCAAI,WAAU;;sDACX,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDAEP,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAI,WAAU;;sEACX,8OAAC,iIAAA,CAAA,QAAK;sEAAC;;;;;;sEACP,8OAAC;4DAAI,WAAU;sEACV,SAAS,GAAG,CAAC,CAAC,SAAS,sBACpB,8OAAC,kIAAA,CAAA,SAAM;oEAEH,MAAK;oEACL,SAAS,iBAAiB,QAAQ,CAAC,QAAQ,KAAK,IAAI,YAAY;oEAChE,WAAU;oEACV,OAAO,QAAQ,KAAK;oEACpB,SAAS,IAAM,cAAc,QAAQ,KAAK;8EAEzC,QAAQ,GAAG;mEAPP,QAAQ,KAAK;;;;;;;;;;;;;;;;8DAalC,8OAAC;oDAAI,WAAU;;sEACX,8OAAC,iIAAA,CAAA,QAAK;sEAAC;;;;;;sEACP,8OAAC,iIAAA,CAAA,QAAK;4DACF,MAAK;4DACL,OAAO,cAAc;4DACrB,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8DAIrD,8OAAC,kIAAA,CAAA,SAAM;oDACH,MAAK;oDACL,SAAQ;oDACR,SAAS;oDACT,WAAU;;sEAEV,8OAAC,kNAAA,CAAA,aAAU;4DAAC,MAAM;4DAAI,WAAU;;;;;;wDAAS;;;;;;;;;;;;;wCAKhD,gBAAgB,MAAM,GAAG,mBACtB,8OAAC;4CAAI,WAAU;;8DACX,8OAAC,iIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,8OAAC;oDAAI,WAAU;8DACV,gBAAgB,GAAG,CAAC,CAAC,UAAU,sBAC5B,8OAAC;4DAAgB,WAAU;;8EACvB,8OAAC;;wEACI,eAAe,SAAS,QAAQ;wEAAE;wEAAK,SAAS,IAAI;;;;;;;8EAEzD,8OAAC,kIAAA,CAAA,SAAM;oEACH,SAAQ;oEACR,MAAK;oEACL,MAAK;oEACL,SAAS,IAAM,qBAAqB;8EAEpC,cAAA,8OAAC,0MAAA,CAAA,SAAM;wEAAC,MAAM;wEAAI,WAAU;;;;;;;;;;;;2DAV1B;;;;;;;;;;;;;;;;;;;;;;8CAqBlC,8OAAC;oCAAI,WAAU;;sDACX,8OAAC,kIAAA,CAAA,SAAM;4CACH,SAAS,MAAM;4CACf,iBAAiB,CAAC,UAAY,SAAS,YAAY;;;;;;sDAEvD,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;;;;;;;8CAGX,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;oCAAS,WAAU;8CAC9C,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAOnD,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM,CAAC,CAAC;gBAAmB,cAAc,IAAM,qBAAqB;0BACxE,cAAA,8OAAC,kIAAA,CAAA,gBAAa;;sCACV,8OAAC,kIAAA,CAAA,eAAY;;8CACT,8OAAC,kIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIvB,8OAAC,kIAAA,CAAA,eAAY;;8CACT,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,qBAAqB;8CAAO;;;;;;8CACrE,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjE"}}, {"offset": {"line": 4632, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}