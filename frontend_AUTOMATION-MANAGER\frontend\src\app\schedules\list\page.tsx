"use client";

import { useState, useEffect, use<PERSON>allback } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { Clock, History, Loader2, Play, Trash2, Terminal, Info, Edit, PlusCircle } from "lucide-react";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogFooter,
} from "@/components/ui/dialog";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    <PERSON><PERSON><PERSON><PERSON>ogHeader,
    <PERSON>ert<PERSON><PERSON>og<PERSON>it<PERSON>,
} from "@/components/ui/alert-dialog";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import AsyncSelect from "react-select/async";
import { apiService } from "@/services/api";
import { ProtectedRoute } from "@/components/ProtectedRoute";

interface ServerInfo {
    _id: string;
    name: string;
    serverUrl?: string; // opcional
}

interface Schedule {
    _id: string;
    name: string;
    serverOriginType: string;
    serverOrigin: ServerInfo;
    serverDestination: ServerInfo;
    appDownloadId: string;
    appDownloadName: string;
    appReplaceId: string;
    appReplaceName: string;
    taskId: string;
    taskName: string;
    nprintingServerId: ServerInfo;
    nprintingTaskId: string;
    nprintingTaskName: string;
    smtpConfigId: string;
    destinatarios: { value: string; label: string }[];
    scheduleType: string;
    scheduleTime?: string;
    customSchedule?: { weekDays: number[]; time: string }[];
    lastExecution?: Date;
    nextExecution?: Date;
    isActive: boolean;
    isExecuting: boolean;
    executionStartTime?: Date;
    executionHistory?: {
        startTime: Date;
        endTime: Date;
        success: boolean;
        steps: {
            step: number;
            status: string;
            message: string;
        }[];
        error?: {
            message: string;
            stack: string;
        };
    }[];
}

interface ExecutionStatus {
    isExecuting: boolean;
    executionStartTime: Date | null;
}

interface ProgressStatus {
    download: number;
    upload: number;
    replace: number;
    task: number;
}

interface LogEntry {
    timestamp: Date;
    message: string;
    progress?: number;
}

interface LogResponse {
    logs: LogEntry[];
    progressStatus: ProgressStatus;
}

interface NPrintingServer {
    _id: string;
    name: string;
    serverUrl: string;
}

interface NPrintingTask {
    id: string;
    name: string;
    description: string;
}

interface SMTPConfig {
    _id: string;
    company: {
        _id: string;
        name: string;
    };
    from: {
        name: string;
        email: string;
    };
}

interface Destinatario {
    _id: string;
    name: string;
    email: string;
    company: {
        _id: string;
        name: string;
    };
}

interface SelectOption {
    value: string;
    label: string;
    stream?: string;
}

export default function ScheduleList() {
    return (
        <ProtectedRoute>
            <ScheduleListPage />
        </ProtectedRoute>
    );
}

function ScheduleListPage() {
    const { toast } = useToast();
    const [loadingStatus, setLoadingStatus] = useState({
        AppsOrigin: false,
        AppsDestination: false,
        Tasks: false,
        NPrintingTasks: false
    });
    const [isLoading, setIsLoading] = useState(false);
    const { control, handleSubmit, watch, setValue } = useForm<Schedule>();
    const [servers, setServers] = useState<{ _id: string; name: string; company: { _id: string; name: string } }[]>([]);
    const [serversDestination, setServersDestination] = useState<{ _id: string; name: string; company: { _id: string; name: string } }[]>([]);
    const [schedules, setSchedules] = useState<Schedule[]>([]);
    const [loading, setLoading] = useState(true);
    const [selectedSchedule, setSelectedSchedule] = useState<Schedule | null>(null);
    const [showHistoryDialog, setShowHistoryDialog] = useState(false);
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    const [showLogsDialog, setShowLogsDialog] = useState(false);
    const [showDetailsDialog, setShowDetailsDialog] = useState(false);
    const [executingSchedules, setExecutingSchedules] = useState<Set<string>>(new Set());
    const [realtimeLogs, setRealtimeLogs] = useState<LogEntry[]>([]);
    const [logPollingInterval, setLogPollingInterval] = useState<NodeJS.Timeout | null>(null);
    const [executionStatus, setExecutionStatus] = useState<Record<string, ExecutionStatus>>({});
    const [progressStatus, setProgressStatus] = useState<ProgressStatus>({
        download: 0,
        upload: 0,
        replace: 0,
        task: 0
    });
    const [checkedSchedules, setCheckedSchedules] = useState<Set<string>>(new Set());
    const [nprintingServers, setNPrintingServers] = useState<NPrintingServer[]>([]);
    const [selectedNPrintingServer, setSelectedNPrintingServer] = useState<NPrintingServer | null>(null);
    const [selectedSMTPConfig, setSelectedSMTPConfig] = useState<SMTPConfig | null>(null);
    const [selectedDestinatarios, setSelectedDestinatarios] = useState<Destinatario[]>([]);
    const [showEditDialog, setShowEditDialog] = useState(false);
    const [editScheduleData, setEditScheduleData] = useState<Schedule | null>(null);

    const [availableDestinatarios, setAvailableDestinatarios] = useState<Destinatario[]>([]);
    const [selectedWeekDays, setSelectedWeekDays] = useState<number[]>([]);
    const [customTime, setCustomTime] = useState("");
    const [appsOrigin, setAppsOrigin] = useState<SelectOption[]>([]);
    const [appsDestination, setAppsDestination] = useState<SelectOption[]>([]);
    const [tasks, setTasks] = useState<SelectOption[]>([]);
    const [nprintingTasks, setNPrintingTasks] = useState<SelectOption[]>([]);
    const [smtpConfigs, setSmtpConfigs] = useState<SMTPConfig[]>([]);
    const [scheduleToExecute, setScheduleToExecute] = useState<string | null>(null);

    const serverOrigin = watch("serverOrigin");
    const serverOriginType = watch("serverOriginType");
    const serverDestination = watch("serverDestination");
    const nprintingServerId = watch("nprintingServerId");
    const [customSchedules, setCustomSchedules] = useState<{ weekDays: number[]; time: string }[]>([]);
    const scheduleType = watch("scheduleType");
    const [destinatarios, setDestinatarios] = useState<SelectOption[]>([]);
    const weekDays = [
        { day: 'D', label: 'Domingo', value: 0 },
        { day: 'S', label: 'Segunda', value: 1 },
        { day: 'T', label: 'Terça', value: 2 },
        { day: 'Q', label: 'Quarta', value: 3 },
        { day: 'Q', label: 'Quinta', value: 4 },
        { day: 'S', label: 'Sexta', value: 5 },
        { day: 'S', label: 'Sábado', value: 6 }
    ];

    const [selectedStream, setSelectedStream] = useState<string>("Todos");
    const [streamOptions, setStreamOptions] = useState<string[]>([]);
    const [selectedStreamDestino, setSelectedStreamDestino] = useState<string>("Todos");
    const [streamOptionsDestino, setStreamOptionsDestino] = useState<string[]>([]);
    const [isLoadingAppsOrigin, setIsLoadingAppsOrigin] = useState(false);
    const [isLoadingAppsDestino, setIsLoadingAppsDestino] = useState(false);

    const checkLoadingStatus = (newStatus: { AppsOrigin: boolean, AppsDestination: boolean, Tasks: boolean, NPrintingTasks: boolean }) => {
        const allLoaded = Object.values(newStatus).every(status => status === true);
        ////console.log("🔹 Verificando status de carregamento:", newStatus);
        ////console.log("🔹 Carregamento atual:", allLoaded);
        if (allLoaded) {
            //setTimeout(() => {
            //    setIsLoading(false);
            //   //console.log("✅ Todos os dados carregados, modal pode ser exibido.");
            //}, 1500); // 🔹 Adiciona um atraso de 1 segundo e meio antes de atualizar o estado
            setIsLoading(false);
        }
    };

    // 🔹 Buscar lista de servidores ao carregar a página
    useEffect(() => {
        if (!serverOriginType) return;

        const fetchServers = async () => {
            try {
                const response = serverOriginType === "cloud"
                    ? await apiService.getQlikCloudServers()
                    : await apiService.getServers();
                console.log("🔹 Servidores encontrados:", response.data);
                setServers(response.data);
            } catch (error) {
                console.error("Erro ao buscar servidores:", error);
            }
        };

        fetchServers();
    }, [serverOriginType]);

    // 🔹 Buscar lista de servidores de destino ao carregar a página
    useEffect(() => {
        const fetchServersDestination = async () => {
            try {
                const response = await apiService.getServers();
                setServersDestination(response.data);
            } catch (error) {
                console.error("Erro ao buscar servidores:", error);
            }
        };

        fetchServersDestination();
    }, []);

    useEffect(() => {
        if (!appsOrigin.length) return;

        const uniqueStreams = [
            "Todos",
            ...Array.from(new Set(appsOrigin.map(app => app.stream)))
        ];

        setStreamOptions(uniqueStreams.filter((stream): stream is string => stream !== undefined));
    }, [appsOrigin]);

    // 🔹 Gerar opções únicas de stream para destino
    useEffect(() => {
        if (!appsDestination.length) return;

        const uniqueStreams = [
            "Todos",
            ...Array.from(new Set(appsDestination.map(app => app.stream)))
        ];

        setStreamOptionsDestino(uniqueStreams.filter((stream): stream is string => stream !== undefined));
    }, [appsDestination]);

    // 🔹 Buscar aplicativos do servidor de origem
    useEffect(() => {
        if (!serverOrigin?._id || !serverOriginType) return;

        const fetchApps = async () => {
            try {
                setIsLoadingAppsOrigin(true);
                const response = serverOriginType === "cloud"
                    ? await apiService.getQlikCloudApps(serverOrigin._id)
                    : await apiService.getQlikApps(serverOrigin._id);
                //console.log("🔹 Aplicativos encontrados:", response.data);
                if (serverOriginType === "cloud") {
                    setAppsOrigin(response.data.map((app: any) => ({
                        value: app.resourceId,
                        label: `Nome: ${app.name} - ID: ${app.resourceId}`,
                        stream: app.stream || "Sem Stream"
                    })));
                } else {
                    setAppsOrigin(response.data.map((app: any) => ({
                        value: app.id,
                        label: `Nome: ${app.name} - ID: ${app.id}`,
                        stream: app.stream || "Sem Stream"
                    })));
                }

            } catch (error) {
                console.error("Erro ao buscar aplicativos:", error);
                setAppsOrigin([]);
            } finally {
                setLoadingStatus(prev => {
                    const newStatus = { ...prev, AppsOrigin: true };
                    ////console.log("🔹 Atualizando loadingStatus:", newStatus);
                    checkLoadingStatus(newStatus);
                    return newStatus;
                });
                setIsLoadingAppsOrigin(false);
            }
        };

        fetchApps();
    }, [serverOrigin, serverOriginType]);

    // 🔹 Buscar aplicativos do servidor de destino
    useEffect(() => {
        if (!serverDestination?._id) return;

        const fetchApps = async () => {
            try {
                setIsLoadingAppsDestino(true);
                const response = await apiService.getQlikApps(serverDestination._id);
                setAppsDestination(response.data.map((app: any) => ({
                    value: app.id,
                    label: `Nome: ${app.name} - ID: ${app.id}`,
                    stream: app.stream || "Sem Stream"
                })));
            } catch (error) {
                console.error("Erro ao buscar aplicativos:", error);
                setAppsDestination([]);
            } finally {
                setLoadingStatus(prev => {
                    const newStatus = { ...prev, AppsDestination: true };
                    ////console.log("🔹 Atualizando loadingStatus:", newStatus);
                    checkLoadingStatus(newStatus);
                    return newStatus;
                });
                setIsLoadingAppsDestino(false);
            }
        };

        fetchApps();
    }, [serverDestination]);

    // 🔹 Buscar tasks do servidor de destino
    useEffect(() => {
        if (!serverDestination?._id) return;

        const fetchTasks = async () => {
            try {
                const response = await apiService.getQlikTasks(serverDestination._id);
                setTasks(response.data.map((task: any) => ({
                    value: task.id,
                    label: `Nome: ${task.name} - ID: ${task.id}`
                })));
            } catch (error) {
                console.error("Erro ao buscar tasks:", error);
                setTasks([]);
            } finally {
                setLoadingStatus(prev => {
                    const newStatus = { ...prev, Tasks: true };
                    ////console.log("🔹 Atualizando loadingStatus:", newStatus);
                    checkLoadingStatus(newStatus);
                    return newStatus;
                });
            }
        };

        fetchTasks();
    }, [serverDestination]);

    // 🔹 Buscar servidores NPrinting
    useEffect(() => {
        const fetchNPrintingServers = async () => {
            try {
                const response = await apiService.getNPrintingServers();
                setNPrintingServers(response.data);
            } catch (error) {
                console.error("Erro ao buscar servidores NPrinting:", error);
                toast({
                    title: "Erro",
                    description: "Falha ao carregar lista de servidores NPrinting.",
                    variant: "destructive",
                });
            }
        };

        fetchNPrintingServers();
    }, []);

    // 🔹 Buscar tasks do servidor NPrinting selecionado
    useEffect(() => {
        if (!nprintingServerId?._id) {
            setNPrintingTasks([]);
            setValue("nprintingTaskId", "");
            setValue("nprintingTaskName", "");
            return;
        }

        const fetchNPrintingTasks = async () => {
            try {
                const response = await apiService.getNPrintingTasks(nprintingServerId._id);
                if (response.data.success) {
                    setNPrintingTasks(response.data.data.map((task: NPrintingTask) => ({
                        value: task.id,
                        label: `Nome: ${task.name} - ID: ${task.id}`
                    })));
                } else {
                    throw new Error(response.data.message || "Erro ao carregar tasks");
                }
            } catch (error) {
                console.error("Erro ao buscar tasks NPrinting:", error);
                toast({
                    title: "Erro",
                    description: "Falha ao carregar lista de tasks NPrinting.",
                    variant: "destructive",
                });
                setNPrintingTasks([]);
            } finally {
                setLoadingStatus(prev => {
                    const newStatus = { ...prev, NPrintingTasks: true };
                    ////console.log("🔹 Atualizando loadingStatus:", newStatus);
                    checkLoadingStatus(newStatus);
                    return newStatus;
                });
            }
        };

        fetchNPrintingTasks();
    }, [nprintingServerId]);

    // 🔹 Buscar lista de SMTPs ao carregar a página
    useEffect(() => {
        const fetchSmtpConfigs = async () => {
            try {
                const response = await apiService.getSMTPConfigs();
                setSmtpConfigs(response.data);
            } catch (error) {
                console.error("Erro ao buscar configurações SMTP:", error);
                toast({
                    title: "Erro",
                    description: "Falha ao carregar lista de configurações SMTP.",
                    variant: "destructive",
                });
            }
        };

        fetchSmtpConfigs();
    }, []);

    // 🔹 Buscar destinatários quando a empresa de origem mudar
    useEffect(() => {
        const selectedServer = servers.find(s => s._id === serverOrigin?._id);
        if (!selectedServer?.company?._id) {
            setDestinatarios([]);
            setValue("destinatarios", []);
            return;
        }

        const fetchDestinatarios = async () => {
            try {
                const response = await apiService.getRecipientsByCompany(selectedServer.company._id);
                if (!response.data.success) {
                    throw new Error(response.data.message || "Falha ao carregar destinatários");
                }

                const options = response.data.data.map((dest: Destinatario) => ({
                    value: dest._id,
                    label: dest.email
                }));
                setDestinatarios(options);
            } catch (error) {
                console.error("Erro ao buscar destinatários:", error);
                toast({
                    title: "Erro",
                    description: "Falha ao carregar lista de destinatários.",
                    variant: "destructive",
                });
                setDestinatarios([]);
            }
        };

        fetchDestinatarios();
    }, [serverOrigin?._id, servers]);

    // Função para atualizar o progresso mantendo o maior valor
    const updateProgress = (current: ProgressStatus, newProgress: ProgressStatus): ProgressStatus => {
        // Se algum valor novo for menor que o atual, mantém o atual
        return {
            download: newProgress.download < current.download ? current.download : newProgress.download,
            upload: newProgress.upload < current.upload ? current.upload : newProgress.upload,
            replace: newProgress.replace < current.replace ? current.replace : newProgress.replace,
            task: newProgress.task < current.task ? current.task : newProgress.task
        };
    };

    // Função para formatar data
    const formatDate = (date: Date | undefined) => {
        if (!date) return "N/A";
        return new Date(date).toLocaleString("pt-BR");
    };

    // 🔹 Função para adicionar horários personalizados
    const addCustomSchedules = () => {
        if (!customTime || selectedWeekDays.length === 0) {
            toast({
                title: "Atenção",
                description: "Selecione pelo menos um dia da semana e um horário",
                variant: "destructive"
            });
            return;
        }

        const newSchedule = {
            weekDays: [...selectedWeekDays].sort((a, b) => a - b),
            time: customTime
        };

        setCustomSchedules([...customSchedules, newSchedule]);
        setSelectedWeekDays([]);
        setCustomTime("");
    };

    // 🔹 Função para alternar seleção de dia da semana
    const toggleWeekDay = (dayValue: number) => {
        setSelectedWeekDays(prev =>
            prev.includes(dayValue)
                ? prev.filter(day => day !== dayValue)
                : [...prev, dayValue]
        );
    };

    // Função para formatar tipo de agendamento
    const formatScheduleType = (type: string) => {
        const types = {
            diario: "Diário",
            semanal: "Semanal",
            mensal: "Mensal",
            personalizado: "Personalizado"
        };
        return types[type as keyof typeof types] || type;
    };

    // Função para calcular duração da execução
    const calculateExecutionDuration = (startTime: Date | string | null) => {
        if (!startTime) return "0m 0s";

        const now = new Date();
        const start = typeof startTime === 'string' ? new Date(startTime) : startTime;

        // Verifica se a data é válida
        if (isNaN(start.getTime())) {
            console.error('Data de início inválida:', startTime);
            return "0m 0s";
        }

        const diffInSeconds = Math.floor((now.getTime() - start.getTime()) / 1000);
        ////console.log('Diferença em segundos:', diffInSeconds, 'Start time:', start.toISOString());

        // Se for mais de 24 horas, mostra em horas
        if (diffInSeconds >= 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return `${hours}h`;
        }

        // Se for mais de 1 hora, mostra em horas e minutos
        if (diffInSeconds >= 3600) {
            const hours = Math.floor(diffInSeconds / 3600);
            const minutes = Math.floor((diffInSeconds % 3600) / 60);
            return `${hours}h ${minutes}m`;
        }

        // Se for menos de 1 hora, mostra em minutos e segundos
        const minutes = Math.floor(diffInSeconds / 60);
        const seconds = diffInSeconds % 60;

        if (minutes === 0) {
            return `${seconds}s`;
        }

        return `${minutes}m ${seconds}s`;
    };

    // Função para buscar logs em tempo real
    const fetchRealtimeLogs = async (scheduleId: string) => {
        try {
            const response = await apiService.getScheduleLogs(scheduleId);
            const data: LogResponse = response.data;

            // Se temos logs, significa que ainda está executando
            if (data.logs && data.logs.length > 0) {
                setExecutingSchedules(prev => new Set(prev).add(scheduleId));

                // Usa o executionStartTime existente
                setExecutionStatus(prev => {
                    const currentStatus = prev[scheduleId];
                    return {
                        ...prev,
                        [scheduleId]: {
                            isExecuting: true,
                            // Mantém o tempo existente, pois o tempo real vem da rota /executing
                            executionStartTime: currentStatus?.executionStartTime
                        }
                    };
                });

                setRealtimeLogs(prevLogs => {
                    const newLogs = processLogs([...prevLogs, ...data.logs]);
                    return newLogs;
                });
            }

            if (data.progressStatus) {
                setProgressStatus(prev => updateProgress(prev, data.progressStatus));
            }
        } catch (error) {
            console.error('Erro ao buscar logs:', error);
        }
    };

    // Função para verificar status de execução dos agendamentos
    const checkExecutionStatus = useCallback(async () => {
        const newExecutionStatus: Record<string, ExecutionStatus> = {};

        for (const schedule of schedules) {
            try {
                const response = await apiService.getScheduleExecutionStatus(schedule._id);
                const data = response.data;

                // Marca como verificado
                setCheckedSchedules(prev => new Set([...prev, schedule._id]));

                if (data.isExecuting) {
                    setExecutingSchedules(prev => new Set(prev).add(schedule._id));
                    newExecutionStatus[schedule._id] = {
                        isExecuting: true,
                        // Sempre usa o startTime do servidor quando disponível
                        executionStartTime: data.executionStartTime ? new Date(data.executionStartTime) : null
                    };

                    // Log para debug
                    if (data.executionStartTime) {
                        //console.log(`Tempo de início recebido do servidor para ${schedule._id}:`, data.executionStartTime);
                    }
                } else {
                    setExecutingSchedules(prev => {
                        const newSet = new Set(prev);
                        newSet.delete(schedule._id);
                        return newSet;
                    });
                    newExecutionStatus[schedule._id] = {
                        isExecuting: false,
                        executionStartTime: null
                    };
                }
            } catch (error) {
                console.error(`Erro ao verificar status de execução para ${schedule._id}:`, error);
                // Em caso de erro, mantém o status anterior
                if (executionStatus[schedule._id]) {
                    newExecutionStatus[schedule._id] = executionStatus[schedule._id];
                }
            }
        }

        setExecutionStatus(newExecutionStatus);
    }, [schedules, executionStatus]);

    // Função para buscar agendamentos
    const fetchSchedules = async () => {
        try {
            setLoading(true);
            const response = await apiService.getSchedules();
            setSchedules(response.data);

            // Verifica o status de execução imediatamente após carregar os agendamentos
            await checkExecutionStatus();
        } catch (error) {
            toast({
                title: "Erro",
                description: "Não foi possível carregar os agendamentos",
                variant: "destructive",
            });
        } finally {
            setLoading(false);
        }
    };

    // Função para buscar histórico de execução
    const fetchExecutionHistory = async (scheduleId: string) => {
        try {
            const response = await apiService.getScheduleHistory(scheduleId);
            return response.data.executionHistory;
        } catch (error) {
            toast({
                title: "Erro",
                description: "Não foi possível carregar o histórico",
                variant: "destructive",
            });
            return [];
        }
    };

    // Função para executar agendamento manualmente
    const executeSchedule = async (id: string) => {
        try {
            // Não definimos mais o executionStartTime aqui, vamos esperar o valor do servidor
            setExecutingSchedules(prev => new Set(prev).add(id));
            setExecutionStatus(prev => ({
                ...prev,
                [id]: {
                    isExecuting: true,
                    executionStartTime: null // Será atualizado quando recebermos o valor do servidor
                }
            }));

            const response = await apiService.executeSchedule(id);
            // A resposta já é tratada pelo apiService

            toast({
                title: "Sucesso",
                description: "Agendamento iniciado com sucesso",
            });

            // Inicia verificação de status
            await checkExecutionStatus();

            // Atualiza a lista apenas uma vez após verificar o status
            await fetchSchedules();

        } catch (error: any) {
            toast({
                title: "Erro",
                description: error.message || "Não foi possível executar o agendamento",
                variant: "destructive",
            });

            // Remove o status de execução em caso de erro
            setExecutingSchedules(prev => {
                const newSet = new Set(prev);
                newSet.delete(id);
                return newSet;
            });
            setExecutionStatus(prev => ({
                ...prev,
                [id]: {
                    isExecuting: false,
                    executionStartTime: null
                }
            }));
        }
    };

    // Função para deletar agendamento
    const deleteSchedule = async () => {
        if (!selectedSchedule) return;

        try {
            await apiService.deleteSchedule(selectedSchedule._id);

            toast({
                title: "Sucesso",
                description: "Agendamento excluído com sucesso",
            });

            setShowDeleteDialog(false);
            fetchSchedules();
        } catch (error) {
            toast({
                title: "Erro",
                description: "Não foi possível excluir o agendamento",
                variant: "destructive",
            });
        }
    };

    // Função para abrir o histórico
    const openHistory = async (schedule: Schedule) => {
        setSelectedSchedule(schedule);
        setShowHistoryDialog(true);
        const history = await fetchExecutionHistory(schedule._id);
        if (history) {
            setSelectedSchedule(prev => prev ? { ...prev, executionHistory: history } : null);
        }
    };

    const resetForm = () => {
        setValue("serverOriginType", "");
        setValue("serverOrigin", { _id: "", name: "" });
        setValue("appDownloadId", "");
        setValue("appDownloadName", "");
        setAppsOrigin([]);
        setValue("serverDestination", { _id: "", name: "" });
        setValue("appReplaceId", "");
        setValue("appReplaceName", "");
        setAppsDestination([]);
        setValue("taskId", "");
        setValue("taskName", "");
        setValue("nprintingServerId", { _id: "", name: "" });
        setValue("nprintingTaskId", "");
        setValue("nprintingTaskName", "");
        setValue("smtpConfigId", "");
        setValue("scheduleType", "");
    };

    // Função para processar logs antes de exibir
    const processLogs = (logs: LogEntry[]) => {
        const processedLogs = new Map<string, LogEntry>();

        logs.forEach(log => {
            const key = log.message;
            // Atualiza apenas o timestamp e progresso se a mensagem já existe
            if (processedLogs.has(key)) {
                const existingLog = processedLogs.get(key)!;
                existingLog.timestamp = log.timestamp;
                if (log.progress !== undefined) {
                    existingLog.progress = log.progress;
                }
            } else {
                processedLogs.set(key, { ...log });
            }
        });

        return Array.from(processedLogs.values())
            .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
    };

    // Função para abrir o dialog de logs
    const openLogsDialog = (schedule: Schedule) => {
        setSelectedSchedule(schedule);
        setShowLogsDialog(true);
        fetchRealtimeLogs(schedule._id);

        // Inicia o polling dos logs
        const interval = setInterval(() => {
            fetchRealtimeLogs(schedule._id);
        }, 2000); // Atualiza a cada 2 segundos

        setLogPollingInterval(interval);
    };

    // Função para limpar logs ao fechar o dialog
    const closeLogsDialog = () => {
        if (logPollingInterval) {
            clearInterval(logPollingInterval);
        }
        setShowLogsDialog(false);
        setRealtimeLogs([]);
        setProgressStatus({
            download: 0,
            upload: 0,
            replace: 0,
            task: 0
        });
    };

    // Efeito para verificar status de execução periodicamente
    useEffect(() => {
        const interval = setInterval(checkExecutionStatus, 5000); // Verifica a cada 5 segundos
        return () => clearInterval(interval);
    }, [checkExecutionStatus]);

    // Carregar agendamentos ao montar o componente
    useEffect(() => {
        fetchSchedules();
    }, []);

    // 🔹 Buscar servidores NPrinting
    useEffect(() => {
        const fetchNPrintingServers = async () => {
            try {
                const response = await apiService.getNPrintingServers();
                setNPrintingServers(response.data);
            } catch (error) {
                console.error("Erro ao buscar servidores NPrinting:", error);
                toast({
                    title: "Erro",
                    description: "Falha ao carregar lista de servidores NPrinting.",
                    variant: "destructive",
                });
            }
        };

        fetchNPrintingServers();
    }, []);

    // Função para formatar dias da semana
    const formatWeekDays = (days: number[]) => {
        const weekDays = [
            'Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'
        ];
        return days.map(day => weekDays[day]).join(', ');
    };

    // Função para renderizar a barra de progresso
    const renderProgressBar = (value: number, label: string) => (
        <div className="space-y-1">
            <div className="flex justify-between text-xs">
                <span>{label}</span>
                <span>{value}%</span>
            </div>
            <div className="h-2 bg-secondary rounded-full overflow-hidden">
                <div
                    className="h-full bg-primary transition-all duration-300 ease-in-out"
                    style={{ width: `${value}%` }}
                />
            </div>
        </div>
    );

    // Função para calcular o progresso geral
    const calculateOverallProgress = (status: ProgressStatus) => {
        const values = Object.values(status);
        return Math.round(values.reduce((acc, val) => acc + val, 0) / values.length);
    };

    // Função para buscar dados detalhados do agendamento
    const fetchScheduleDetails = async (schedule: Schedule) => {
        try {
            //console.log('Schedule details:', schedule); // Debug

            // Buscar servidor NPrinting
            if (schedule.nprintingServerId) {
                //console.log('Buscando servidor NPrinting:', schedule.nprintingServerId._id); // Debug
                try {
                    const nprintingResponse = await apiService.getNPrintingServer(schedule.nprintingServerId._id);
                    //console.log('NPrinting response:', nprintingResponse.data); // Debug
                    setSelectedNPrintingServer(nprintingResponse.data);
                } catch (error) {
                    console.error('Erro ao buscar servidor NPrinting:', error);
                    toast({
                        title: "Aviso",
                        description: "Não foi possível carregar os dados do servidor NPrinting",
                        variant: "destructive",
                    });
                }
            }

            // Buscar configuração SMTP
            if (schedule.smtpConfigId) {
                //console.log('Buscando configuração SMTP:', schedule.smtpConfigId); // Debug
                try {
                    const smtpResponse = await apiService.getSMTPConfig(schedule.smtpConfigId);
                    //console.log('SMTP response:', smtpResponse.data); // Debug
                    setSelectedSMTPConfig(smtpResponse.data);
                } catch (error) {
                    console.error('Erro ao buscar configuração SMTP:', error);
                    toast({
                        title: "Aviso",
                        description: "Não foi possível carregar as configurações SMTP",
                        variant: "destructive",
                    });
                }
            }

            // Buscar detalhes dos destinatários
            if (schedule.destinatarios?.length > 0) {
                //console.log('Buscando destinatários:', schedule.destinatarios); // Debug
                try {
                    const destinatariosResponse = await apiService.getRecipientsByIds(schedule.destinatarios);
                    //console.log('Destinatários response:', destinatariosResponse.data); // Debug

                    if (destinatariosResponse.data.success) {
                        setSelectedDestinatarios(destinatariosResponse.data.data);
                        /*setValue("destinatarios", destinatariosResponse.data.data.map((destinatario: Destinatario) => ({
                            value: destinatario._id,
                            label: destinatario.email
                        })));*/
                    } else {
                        console.error('Erro ao buscar destinatários:', destinatariosResponse.data.message);
                        toast({
                            title: "Aviso",
                            description: "Não foi possível carregar os detalhes dos destinatários",
                            variant: "destructive",
                        });
                    }
                } catch (error) {
                    console.error('Erro ao buscar destinatários:', error);
                    toast({
                        title: "Aviso",
                        description: "Não foi possível carregar os detalhes dos destinatários",
                        variant: "destructive",
                    });
                }
            }
        } catch (error) {
            console.error('Erro ao buscar detalhes do agendamento:', error);
            toast({
                title: "Erro",
                description: "Não foi possível carregar todos os detalhes do agendamento",
                variant: "destructive",
            });
        }
    };

    // Função para abrir o dialog de detalhes
    const openDetailsDialog = async (schedule: Schedule) => {
        console.log('Abrindo dialog de detalhes:', schedule);
        setSelectedSchedule(schedule);
        setShowDetailsDialog(true);
        // Limpar estados anteriores
        setSelectedNPrintingServer(null);
        setSelectedSMTPConfig(null);
        setSelectedDestinatarios([]);
        // Buscar novos dados
        await fetchScheduleDetails(schedule);
    };

    const openEditDialog = async (schedule: Schedule) => {
        setIsLoading(true); // 🔄 Inicia o carregamento
        setSelectedSchedule(schedule);
        //console.log('Schedule:', JSON.stringify(schedule));
        setValue("name", schedule.name);
        setValue("serverOriginType", schedule.serverOriginType);
        setValue("serverOrigin", schedule.serverOrigin);
        setValue("appDownloadId", schedule.appDownloadId);
        setValue("appDownloadName", schedule.appDownloadName);
        setValue("serverDestination", schedule.serverDestination);
        setValue("appReplaceId", schedule.appReplaceId);
        setValue("appReplaceName", schedule.appReplaceName);
        setValue("taskId", schedule.taskId);
        setValue("taskName", schedule.taskName);
        setValue("nprintingServerId", schedule.nprintingServerId);
        ////console.log('NPrinting Server:', schedule.nprintingServerId);
        setValue("nprintingTaskId", schedule.nprintingTaskId);
        setValue("nprintingTaskName", schedule.nprintingTaskName);
        setValue("smtpConfigId", schedule.smtpConfigId);
        setValue("scheduleType", schedule.scheduleType);
        setValue("isActive", schedule.isActive);
        if (schedule.scheduleType !== "personalizado") {
            setValue("scheduleTime", schedule.scheduleTime);
        } else {
            setCustomSchedules(schedule.customSchedule || []);
        }

        // Buscar destinatários atualizados
        try {
            //console.log("🚀 Iniciando busca de destinatários...");
            //console.log("🔍 IDs dos destinatários enviados:", schedule.destinatarios);

            const response = await apiService.getRecipientsByIds(schedule.destinatarios);
            //console.log("✅ Resposta recebida:", response.data);

            if (response.data.success) {
                //console.log("📩 Destinatários encontrados:", response.data.data);

                const destinatariosOptions = response.data.data.map((dest: any) => ({
                    value: dest._id,
                    label: dest.email
                }));
                //setDestinatarios(destinatariosOptions);
                //console.log("🎯 Destinatários formatados:", destinatariosOptions);

                // Definir os destinatários selecionados
                //const selectedDestinatarios = schedule.destinatarios.map(dest => ({
                //    value: dest.value,
                //    label: dest.label
                //}));
                setValue("destinatarios", destinatariosOptions);
                //console.log("✅ Destinatários selecionados definidos:", selectedDestinatarios);
            } else {
                console.warn("⚠️ Erro na resposta da API:", response.data.message);
                toast({
                    title: "Erro",
                    description: response.data.message || "Falha ao carregar lista de destinatários.",
                    variant: "destructive",
                });
            }

        } catch (error) {
            console.error("❌ Erro ao carregar destinatários:", error);
            toast({
                title: "Erro",
                description: "Falha ao carregar lista de destinatários.",
                variant: "destructive",
            });
        }
        setShowEditDialog(true);
    };

    // Função para buscar destinatários disponíveis
    /*const fetchAvailableDestinatarios = async (companyId: string) => {
        try {
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/destinatarios/list/${companyId}`);
            const data = await response.json();

            if (data.success) {
                setAvailableDestinatarios(data.data);
            } else {
                console.error('Erro ao buscar destinatários:', data.message);
            }
        } catch (error) {
            console.error('Erro ao buscar destinatários:', error);
        }
    };*/

    // Efeito para carregar destinatários quando o modal de edição é aberto
    /*useEffect(() => {
        if (editScheduleData?.serverOrigin?.company?._id) {
            fetchAvailableDestinatarios(editScheduleData.serverOrigin.company._id);
        }
    }, [editScheduleData]);

    // Função para adicionar um novo agendamento personalizado
    const addCustomSchedule = () => {
        if (selectedWeekDays.length > 0 && customTime) {
            setEditFormData(prev => ({
                ...prev,
                customSchedule: [
                    ...prev.customSchedule,
                    { weekDays: selectedWeekDays, time: customTime }
                ]
            }));
            setSelectedWeekDays([]);
            setCustomTime("");
        }
    };*/

    // Função para remover um agendamento personalizado
    const removeCustomSchedule = (index: number) => {
        const updatedSchedules = [...customSchedules];
        updatedSchedules.splice(index, 1);
        setCustomSchedules(updatedSchedules);
    };

    // 🔹 Submissão do formulário de edição
    const onSubmit = async (formData: Schedule) => {
        setLoading(true);
        try {
            // Validações básicas

            if (!formData.name) {
                throw new Error("Nome do agendamento é obrigatório");
            }

            if (!formData.serverOriginType) {
                throw new Error("Tipo de servidor de origem é obrigatório");
            }

            if (!formData.serverOrigin || !formData.appDownloadId || !formData.appDownloadName) {
                throw new Error("Servidor de origem, ID e nome do aplicativo para download são obrigatórios");
            }

            if (!formData.serverDestination || !formData.appReplaceId || !formData.appReplaceName) {
                throw new Error("Servidor de destino, ID e nome do aplicativo para replace são obrigatórios");
            }

            if (!formData.taskId || !formData.taskName) {
                throw new Error("Task para execução e nome são obrigatórios");
            }

            if (!formData.nprintingServerId._id || !formData.nprintingTaskId || !formData.nprintingTaskName) {
                throw new Error("Servidor NPrinting, ID e nome da task NPrinting são obrigatórios");
            }

            if (!formData.smtpConfigId) {
                throw new Error("Remetente é obrigatório");
            }

            if (!formData.destinatarios || formData.destinatarios.length === 0) {
                throw new Error("Selecione pelo menos um destinatário");
            }

            if (!formData.scheduleType) {
                throw new Error("Tipo de agendamento é obrigatório");
            }

            // Validação específica para cada tipo de agendamento
            if (formData.scheduleType !== "personalizado" && !formData.scheduleTime) {
                throw new Error("Horário de execução é obrigatório");
            }

            if (formData.scheduleType === "personalizado" && (!customSchedules || customSchedules.length === 0)) {
                throw new Error("Adicione pelo menos um agendamento personalizado");
            }

            // Estrutura do payload
            const updatedData = {
                name: formData.name,
                serverOriginType: formData.serverOriginType,
                serverOrigin: {
                    _id: formData.serverOrigin._id,
                    name: formData.serverOrigin.name
                },
                appDownloadId: formData.appDownloadId,
                appDownloadName: formData.appDownloadName,
                serverDestination: {
                    _id: formData.serverDestination._id,
                    name: formData.serverDestination.name
                },
                appReplaceId: formData.appReplaceId,
                appReplaceName: formData.appReplaceName,
                taskId: formData.taskId,
                taskName: formData.taskName,
                nprintingServerId: formData.nprintingServerId._id,
                nprintingTaskId: formData.nprintingTaskId,
                nprintingTaskName: formData.nprintingTaskName,
                smtpConfigId: formData.smtpConfigId,
                destinatarios: formData.destinatarios.map(dest => dest.value),
                scheduleType: formData.scheduleType,
                scheduleTime: formData.scheduleType !== "personalizado" ? formData.scheduleTime : undefined,
                customSchedule: formData.scheduleType === "personalizado" ? customSchedules.map(schedule => ({
                    weekDays: schedule.weekDays,
                    time: schedule.time
                })) : undefined,
                isActive: formData.isActive
            };

            //console.log("updatedData sendo enviado:", updatedData); // Log para debug

            const response = await apiService.updateSchedule(selectedSchedule?._id!, updatedData);

            if (response.data.success) {
                toast({
                    title: "Sucesso",
                    description: "Agendamento atualizado com sucesso",
                });
                setShowEditDialog(false);
                fetchSchedules(); // Atualiza a lista
                // Limpar formulário após sucesso
                resetForm();
            } else {
                toast({
                    title: "Erro",
                    description: response.data.message || "Não foi possível atualizar o agendamento",
                    variant: "destructive",
                });
            }


        } catch (error: any) {
            console.error("Erro ao criar agendamento:", error);
            const errorMessage = error.response?.data?.message || error.message || "Ocorreu um erro inesperado.";
            toast({
                title: "Erro ao criar agendamento",
                description: errorMessage,
                variant: "destructive"
            });
        } finally {
            setLoading(false);
        }
    };

    const handleExecuteClick = (scheduleId: string) => {
        setScheduleToExecute(scheduleId);
    };

    const handleConfirmExecution = async () => {
        if (scheduleToExecute) {
            setScheduleToExecute(null); // Fecha o modal antes de executar
            await executeSchedule(scheduleToExecute);
        }
    };



    return (
        <div className="container mx-auto py-6">
            <Card className="m-5">
                <CardHeader>
                    <CardTitle>Lista de Agendamentos</CardTitle>
                </CardHeader>
                <CardContent>
                    {loading ? (
                        <div className="flex justify-center items-center h-32">
                            <Loader2 className="h-8 w-8 animate-spin" />
                        </div>
                    ) : (
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    {/*<TableHead>Servidor Origem</TableHead>
                                    <TableHead>App Download</TableHead>
                                    <TableHead>Servidor Destino</TableHead>
                                    <TableHead>App Replace</TableHead>*/}
                                    <TableHead>Nome</TableHead>
                                    <TableHead>Tipo</TableHead>
                                    <TableHead>Última Execução</TableHead>
                                    <TableHead>Próxima Execução</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead>Ações</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {schedules.map((schedule) => (
                                    <TableRow key={schedule._id}>
                                        {/*<TableCell>{schedule.serverOrigin.name}</TableCell>
                                        <TableCell>
                                            <div className="text-sm">
                                                <p>ID: {schedule.appDownloadId || 'N/A'}</p>
                                                <p>Nome: {schedule.appDownloadName || 'N/A'}</p>
                                            </div>
                                        </TableCell>
                                        <TableCell>{schedule.serverDestination.name}</TableCell>
                                        <TableCell>
                                            <div className="text-sm">
                                                <p>ID: {schedule.appReplaceId || 'N/A'}</p>
                                                <p>Nome: {schedule.appReplaceName || 'N/A'}</p>
                                            </div>
                                        </TableCell>*/}
                                        <TableCell>{schedule.name}</TableCell>
                                        <TableCell>{formatScheduleType(schedule.scheduleType)}</TableCell>
                                        <TableCell>{formatDate(schedule.lastExecution)}</TableCell>
                                        <TableCell>{formatDate(schedule.nextExecution)}</TableCell>
                                        <TableCell>
                                            <div className="flex flex-col gap-1">
                                                <Badge variant={schedule.isActive ? "default" : "secondary"} className="flex justify-center">
                                                    {schedule.isActive ? "Ativo" : "Inativo"}
                                                </Badge>
                                                {executionStatus[schedule._id]?.isExecuting && (
                                                    <Badge variant="outline" className="animate-pulse flex justify-center text-center">
                                                        Em execução há {calculateExecutionDuration(executionStatus[schedule._id].executionStartTime!)}
                                                    </Badge>
                                                )}
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex gap-2">
                                                <Button
                                                    variant="outline"
                                                    size="icon"
                                                    onClick={() => openHistory(schedule)}
                                                    title="Ver histórico"
                                                >
                                                    <History className="h-4 w-4" />
                                                </Button>
                                                <Button
                                                    variant="outline"
                                                    size="icon"
                                                    onClick={() => openEditDialog(schedule)}
                                                    title="Editar"
                                                    disabled={executionStatus[schedule._id]?.isExecuting}
                                                >
                                                    <Edit className="h-4 w-4" />
                                                </Button>
                                                <Button
                                                    variant="outline"
                                                    size="icon"
                                                    onClick={() => handleExecuteClick(schedule._id)}
                                                    disabled={!checkedSchedules.has(schedule._id) || executionStatus[schedule._id]?.isExecuting}
                                                    title="Executar agora"
                                                >
                                                    {!checkedSchedules.has(schedule._id) || executionStatus[schedule._id]?.isExecuting ? (
                                                        <Loader2 className="h-4 w-4 animate-spin" />
                                                    ) : (
                                                        <Play className="h-4 w-4" />
                                                    )}
                                                </Button>
                                                {executionStatus[schedule._id]?.isExecuting && (
                                                    <Button
                                                        variant="outline"
                                                        size="icon"
                                                        onClick={() => openLogsDialog(schedule)}
                                                        title="Ver logs em tempo real"
                                                    >
                                                        <Terminal className="h-4 w-4" />
                                                    </Button>
                                                )}
                                                <Button
                                                    variant="outline"
                                                    size="icon"
                                                    onClick={() => openDetailsDialog(schedule)}
                                                    title="Ver detalhes"
                                                >
                                                    <Info className="h-4 w-4" />
                                                </Button>
                                                <Button
                                                    variant="destructive"
                                                    size="icon"
                                                    onClick={() => {
                                                        setSelectedSchedule(schedule);
                                                        setShowDeleteDialog(true);
                                                    }}
                                                    title="Excluir"
                                                    disabled={executionStatus[schedule._id]?.isExecuting}
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    )}
                </CardContent>
            </Card>

            {/* Dialog do Histórico */}
            <Dialog open={showHistoryDialog} onOpenChange={setShowHistoryDialog}>
                <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Histórico de Execuções</DialogTitle>
                        <DialogDescription>
                            Detalhes das execuções do agendamento
                        </DialogDescription>
                    </DialogHeader>
                    {selectedSchedule?.executionHistory?.length === 0 ? (
                        <div className="text-center py-4 text-muted-foreground">
                            Nenhuma execução registrada
                        </div>
                    ) : (
                        [...(selectedSchedule?.executionHistory || [])]
                            .sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime())
                            .map((execution, index) => (
                                <Card key={index} className="mb-4">
                                    <CardHeader>
                                        <CardTitle className="text-sm flex justify-between items-center">
                                            <span className="flex items-center gap-2">
                                                <Clock className="h-4 w-4" />
                                                Execução em {formatDate(execution.startTime)}
                                            </span>
                                            <Badge variant={execution.success ? "default" : "destructive"}>
                                                {execution.success ? "Sucesso" : "Falha"}
                                            </Badge>
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-2">
                                            {execution.steps.map((step, stepIndex) => (
                                                <div key={stepIndex} className="flex items-center gap-2">
                                                    <Badge variant={step.status === "concluído" ? "default" : "secondary"} className="min-w-[75px] flex justify-center">
                                                        Passo {step.step}
                                                    </Badge>
                                                    <span>{step.message}</span>
                                                </div>
                                            ))}
                                            {execution.error && (
                                                <div className="mt-2 p-2 bg-red-50 text-red-700 rounded">
                                                    <p className="font-semibold">Erro:</p>
                                                    <p>{execution.error.message}</p>
                                                </div>
                                            )}
                                        </div>
                                    </CardContent>
                                </Card>
                            ))
                    )}
                </DialogContent>
            </Dialog>

            {/* Dialog de Confirmação de Exclusão */}
            <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>
                        <AlertDialogDescription>
                            Tem certeza que deseja excluir este agendamento? Esta ação não pode ser desfeita.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancelar</AlertDialogCancel>
                        <AlertDialogAction onClick={deleteSchedule}>
                            Confirmar
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Dialog de Logs em Tempo Real */}
            <Dialog open={showLogsDialog} onOpenChange={closeLogsDialog}>
                <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Logs em Tempo Real</DialogTitle>
                        <DialogDescription>
                            {selectedSchedule && executionStatus[selectedSchedule._id]?.executionStartTime && (
                                <div className="space-y-4">
                                    <span>
                                        Execução iniciada há {calculateExecutionDuration(executionStatus[selectedSchedule._id].executionStartTime!)}
                                    </span>
                                    <div className="space-y-4 mt-4">
                                        {/* Barra de Progresso Geral */}
                                        {renderProgressBar(calculateOverallProgress(progressStatus), "Progresso Geral")}

                                        {/* Barras de Progresso Individuais */}
                                        <div className="grid grid-cols-2 gap-4">
                                            {renderProgressBar(progressStatus.download, "Download")}
                                            {renderProgressBar(progressStatus.upload, "Upload")}
                                            {renderProgressBar(progressStatus.replace, "Substituição")}
                                            {renderProgressBar(progressStatus.task, "Execução da Task")}
                                        </div>
                                    </div>
                                </div>
                            )}
                        </DialogDescription>
                    </DialogHeader>
                    <div className="bg-black text-green-400 p-4 rounded font-mono text-sm mt-4">
                        {realtimeLogs.length === 0 ? (
                            <div className="text-gray-500">Aguardando logs...</div>
                        ) : (
                            <div className="space-y-2">
                                {realtimeLogs.reduce((acc: JSX.Element[], log, index) => {
                                    // Extrai a parte base da mensagem (sem a porcentagem)
                                    const baseMessage = log.message.replace(/: \d+%$/, '');
                                    
                                    // Verifica se já existe uma mensagem similar
                                    const similarMessageIndex = acc.findIndex(
                                        (item) => item.key && 
                                        realtimeLogs[parseInt(item.key as string)]?.message.replace(/: \d+%$/, '') === baseMessage
                                    );

                                    // Se encontrou mensagem similar e tem progresso, atualiza ela
                                    if (similarMessageIndex !== -1 && log.progress !== undefined) {
                                        acc[similarMessageIndex] = (
                                            <div key={index} className="flex items-start gap-2">
                                                <span className="text-gray-500 whitespace-nowrap">
                                                    [{new Date(log.timestamp).toLocaleTimeString()}]
                                                </span>
                                                <div className="flex-1">
                                                    <span>{log.message}</span>
                                                    {log.progress !== undefined && (
                                                        <div className="h-1 bg-gray-700 rounded-full mt-1 overflow-hidden">
                                                            <div
                                                                className="h-full bg-green-400 rounded-full transition-all duration-300"
                                                                style={{ width: `${log.progress}%` }}
                                                            />
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        );
                                    } 
                                    // Se não encontrou mensagem similar ou não tem progresso, adiciona nova
                                    else if (similarMessageIndex === -1) {
                                        acc.push(
                                            <div key={index} className="flex items-start gap-2">
                                                <span className="text-gray-500 whitespace-nowrap">
                                                    [{new Date(log.timestamp).toLocaleTimeString()}]
                                                </span>
                                                <div className="flex-1">
                                                    <span>{log.message}</span>
                                                    {log.progress !== undefined && (
                                                        <div className="h-1 bg-gray-700 rounded-full mt-1 overflow-hidden">
                                                            <div
                                                                className="h-full bg-green-400 rounded-full transition-all duration-300"
                                                                style={{ width: `${log.progress}%` }}
                                                            />
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        );
                                    }
                                    return acc;
                                }, [])}
                            </div>
                        )}
                    </div>
                </DialogContent>
            </Dialog>

            {/* Dialog de Detalhes */}
            <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
                <DialogContent className="max-w-3xl">
                    <DialogHeader>
                        <DialogTitle>Detalhes do Agendamento</DialogTitle>
                        <DialogDescription>
                            {selectedSchedule?.name}
                        </DialogDescription>
                    </DialogHeader>
                    {selectedSchedule && (
                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-4">
                                <div>
                                    <h3 className="font-semibold mb-2">Tipo de Servidor de Origem</h3>
                                    <p className="text-sm">{selectedSchedule.serverOriginType === "qrs" ? "Qlik Sense Enterprise Client Manager" : "Qlik Sense Cloud"}</p>
                                </div>
                                <div>
                                    <h3 className="font-semibold mb-2">Servidor de Origem</h3>
                                    <p className="text-sm">{selectedSchedule.serverOrigin.name}</p>
                                </div>
                                <div>
                                    <h3 className="font-semibold mb-2">App para Download</h3>
                                    <div className="text-sm">
                                        <p>ID: {selectedSchedule.appDownloadId || 'N/A'}</p>
                                        <p>Nome: {selectedSchedule.appDownloadName || 'N/A'}</p>
                                    </div>
                                </div>
                                <div>
                                    <h3 className="font-semibold mb-2">Servidor de Destino</h3>
                                    <p className="text-sm">{selectedSchedule.serverDestination.name}</p>
                                </div>
                                <div>
                                    <h3 className="font-semibold mb-2">App para Replace</h3>
                                    <div className="text-sm">
                                        <p>ID: {selectedSchedule.appReplaceId || 'N/A'}</p>
                                        <p>Nome: {selectedSchedule.appReplaceName || 'N/A'}</p>
                                    </div>
                                </div>
                                <div>
                                    <h3 className="font-semibold mb-2">Task de Execução</h3>
                                    <div className="text-sm">
                                        <p>ID: {selectedSchedule.taskId || 'N/A'}</p>
                                        <p>Nome: {selectedSchedule.taskName || 'N/A'}</p>
                                    </div>
                                </div>
                                <div>
                                    <h3 className="font-semibold mb-2">Servidor NPrinting</h3>
                                    <p className="text-sm">
                                        {selectedNPrintingServer ? (
                                            selectedNPrintingServer.name
                                        ) : (
                                            selectedSchedule?.nprintingServerId ? 'Carregando...' : 'N/A'
                                        )}
                                    </p>
                                </div>
                                <div>
                                    <h3 className="font-semibold mb-2">Configuração SMTP</h3>
                                    {selectedSMTPConfig ? (
                                        <div className="text-sm">
                                            <p>Empresa: {selectedSMTPConfig.company.name}</p>
                                            <p>Remetente: {selectedSMTPConfig.from.name}</p>
                                            <p>E-mail: {selectedSMTPConfig.from.email}</p>
                                        </div>
                                    ) : (
                                        <p className="text-sm">Carregando...</p>
                                    )}
                                </div>
                            </div>
                            <div className="space-y-4">
                                <div>
                                    <h3 className="font-semibold mb-2">Task NPrinting</h3>
                                    <div className="text-sm">
                                        <p>ID: {selectedSchedule.nprintingTaskId || 'N/A'}</p>
                                        <p>Nome: {selectedSchedule.nprintingTaskName || 'N/A'}</p>
                                    </div>
                                </div>
                                <div>
                                    <h3 className="font-semibold mb-2">Tipo de Agendamento</h3>
                                    <p className="text-sm">{formatScheduleType(selectedSchedule.scheduleType)}</p>
                                    {selectedSchedule.scheduleType !== 'personalizado' ? (
                                        <p className="text-sm text-muted-foreground">Horário: {selectedSchedule.scheduleTime}</p>
                                    ) : (
                                        <div className="mt-2">
                                            {selectedSchedule.customSchedule?.map((schedule, index) => (
                                                <p key={index} className="text-sm text-muted-foreground">
                                                    {formatWeekDays(schedule.weekDays)} às {schedule.time}
                                                </p>
                                            ))}
                                        </div>
                                    )}
                                </div>
                                <div>
                                    <h3 className="font-semibold mb-2">Destinatários (Empresa de Origem)</h3>
                                    <div className="max-h-24 overflow-y-auto">
                                        {selectedDestinatarios.length > 0 ? (
                                            selectedDestinatarios.map((destinatario) => (
                                                <div key={destinatario._id} className="text-sm mb-1">
                                                    <p>Nome: {destinatario.name}</p>
                                                    <p className="text-muted-foreground">E-mail: {destinatario.email}</p>
                                                </div>
                                            ))
                                        ) : (
                                            <p className="text-sm">Carregando destinatários...</p>
                                        )}
                                    </div>
                                </div>
                                <div>
                                    <h3 className="font-semibold mb-2">Status</h3>
                                    <div className="space-y-2">
                                        <Badge variant={selectedSchedule.isActive ? "default" : "secondary"}>
                                            {selectedSchedule.isActive ? "Ativo" : "Inativo"}
                                        </Badge>
                                        {executionStatus[selectedSchedule._id]?.isExecuting && (
                                            <Badge variant="outline" className="animate-pulse ml-2">
                                                Em execução
                                            </Badge>
                                        )}
                                    </div>
                                </div>
                                <div>
                                    <h3 className="font-semibold mb-2">Execuções</h3>
                                    <p className="text-sm">Última: {formatDate(selectedSchedule.lastExecution)}</p>
                                    <p className="text-sm">Próxima: {formatDate(selectedSchedule.nextExecution)}</p>
                                </div>
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>

            {/* Dialog de Edição */}
            <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
                <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Editar Agendamento</DialogTitle>
                        <DialogDescription>
                            Atualize as informações do agendamento
                        </DialogDescription>
                    </DialogHeader>
                    {
                        selectedSchedule && (
                            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">

                                {isLoading && (
                                    <div className="absolute w-[99.3%] -ml-[20px] h-screen z-15 bg-gray-500 bg-opacity-50 rounded-lg" style={{ zIndex: 15 }}>
                                        <div className="absolute top-[10%] left-[37%] z-10 flex flex-col items-center justify-center bg-white p-4 rounded-lg shadow-lg text-[20px]">
                                            <span className="text-gray-600">Carregando dados...</span>
                                            <div className="flex justify-center items-center h-32">
                                                <Loader2 className="h-16 w-16 animate-spin" />
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* 🔹 Nome do Agendamento */}
                                <div>
                                    <Label>Nome do Agendamento</Label>
                                    <Controller
                                        name="name"
                                        control={control}
                                        defaultValue={selectedSchedule?.name}
                                        render={({ field }) => (
                                            <Input
                                                placeholder="Digite o nome do agendamento"
                                                {...field}
                                            />
                                        )}
                                    />
                                </div>

                                {/* 🔹 Servidor de Origem */}
                                <div>
                                    <Label>Tipo de Servidor de Origem</Label>
                                    <Controller
                                        name="serverOriginType"
                                        control={control}
                                        defaultValue={selectedSchedule?.serverOriginType}
                                        render={({ field }) => (
                                            <Select
                                                value={field.value}
                                                onValueChange={(value) => {
                                                    field.onChange(value);
                                                    setValue("serverOrigin", { _id: "", name: "" });
                                                    setValue("appDownloadId", "");
                                                    setValue("appDownloadName", "");
                                                    setAppsOrigin([]);
                                                }}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Selecione um tipo de servidor" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="qrs">Qlik Sense Enterprise Client Manager</SelectItem>
                                                    <SelectItem value="cloud">Qlik Sense Cloud</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        )}
                                    />
                                </div>

                                {/* 🔹 Servidor de Origem */}
                                <div>
                                    <Label>Servidor de Origem</Label>
                                    <Controller
                                        name="serverOrigin"
                                        control={control}
                                        defaultValue={selectedSchedule?.serverOrigin}
                                        render={({ field }) => (
                                            <Select
                                                value={field.value?._id || ""}
                                                disabled={!serverOriginType}
                                                onValueChange={(value) => {
                                                    field.onChange({ _id: value, name: servers.find(s => s._id === value)?.name || "" });
                                                    setAppsOrigin([]);
                                                    setValue("appDownloadId", "");
                                                    setValue("appDownloadName", "");
                                                }}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Selecione um servidor" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {servers.map((server) => (
                                                        <SelectItem key={server._id} value={server._id}>
                                                            {server.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        )}
                                    />
                                </div>

                                {/* 🔹 Filtrar por Stream */}
                                <div className="mb-4">
                                    <Label>Filtrar App de Download por Stream</Label>
                                    <Select
                                        onValueChange={setSelectedStream}
                                        value={selectedStream}
                                        disabled={!serverOrigin?._id} // ✅ desabilita se não tiver servidor
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Selecione a stream" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {isLoadingAppsOrigin ? (
                                                <SelectItem value="loading" disabled className="flex justify-center items-center gap-2">
                                                    Carregando streams...
                                                </SelectItem>
                                            ) : streamOptions.length <= 0 ?
                                                <SelectItem value="Todos">Todos</SelectItem>
                                                :
                                                streamOptions.map(stream => (
                                                    <SelectItem key={stream} value={stream}>
                                                        {stream}
                                                    </SelectItem>
                                                ))
                                            }
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* 🔹 Aplicativo para Download */}
                                <div>
                                    <Label>Aplicativo para Download</Label>
                                    <Controller
                                        name="appDownloadId"
                                        control={control}
                                        defaultValue={selectedSchedule?.appDownloadId}
                                        render={({ field }) => (
                                            <AsyncSelect<SelectOption>
                                                value={appsOrigin.find(option => option.value === field.value) || null}
                                                instanceId="app-download-select"
                                                cacheOptions
                                                defaultOptions={appsOrigin.filter(app =>
                                                    selectedStream === "Todos" || app.stream === selectedStream
                                                )}
                                                loadOptions={(inputValue: string, callback: (options: SelectOption[]) => void) => {
                                                    const filteredOptions = appsOrigin.filter((option) =>
                                                        option.label.toLowerCase().includes(inputValue.toLowerCase())
                                                    );
                                                    callback(filteredOptions);
                                                }}
                                                isDisabled={!serverOrigin?._id}
                                                onChange={(option: SelectOption | null) => {
                                                    field.onChange(option?.value);
                                                    const name = option?.label.split(' - ID:')[0].replace('Nome: ', '') || '';
                                                    setValue("appDownloadName", name);
                                                }}
                                                placeholder="Selecione um aplicativo"
                                                /** 🔹 Mensagem enquanto carrega */
                                                isLoading={isLoadingAppsOrigin}
                                                loadingMessage={() => "Carregando aplicativos..."}
                                                noOptionsMessage={() => "Nenhum aplicativo encontrado"}
                                            />
                                        )}
                                    />
                                </div>

                                {/* 🔹 Servidor de Destino */}
                                <div>
                                    <Label>Servidor de Destino</Label>
                                    <Controller
                                        name="serverDestination"
                                        control={control}
                                        defaultValue={selectedSchedule?.serverDestination}
                                        render={({ field }) => (
                                            <Select
                                                value={field.value?._id || ""}
                                                onValueChange={(value) => {
                                                    field.onChange({ _id: value, name: serversDestination.find(s => s._id === value)?.name || "" });
                                                    setAppsDestination([]);
                                                    setTasks([]);
                                                    setValue("appReplaceId", "");
                                                    setValue("appReplaceName", "");
                                                    setValue("taskId", "");
                                                    setValue("taskName", "");
                                                }}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Selecione um servidor" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {serversDestination.map((server) => (
                                                        <SelectItem key={server._id} value={server._id}>
                                                            {server.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        )}
                                    />
                                </div>

                                {/* 🔹 Filtrar App de Replace por Stream */}
                                <div className="mb-4">
                                    <Label>Filtrar App de Replace por Stream</Label>
                                    <Select
                                        onValueChange={setSelectedStreamDestino}
                                        value={selectedStreamDestino}
                                        disabled={!serverDestination?._id}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Selecione a stream" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {isLoadingAppsDestino ? (
                                                <SelectItem value="loading" disabled className="flex justify-center items-center gap-2">
                                                    Carregando streams...
                                                </SelectItem>
                                            ) : streamOptionsDestino.length <= 0 ?
                                                <SelectItem value="Todos">Todos</SelectItem>
                                                :
                                                streamOptionsDestino.map(stream => (
                                                    <SelectItem key={stream} value={stream}>{stream}</SelectItem>
                                                ))
                                            }
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* 🔹 Aplicativo para Replace */}
                                <div>
                                    <Label>Aplicativo para Replace</Label>
                                    <Controller
                                        name="appReplaceId"
                                        defaultValue={selectedSchedule?.appReplaceId}
                                        control={control}
                                        render={({ field }) => (
                                            <AsyncSelect<SelectOption>
                                                value={appsDestination.find(option => option.value === field.value) || null}
                                                instanceId="app-replace-select"
                                                cacheOptions
                                                defaultOptions={appsDestination.filter(app =>
                                                    selectedStreamDestino === "Todos" || app.stream === selectedStreamDestino
                                                )}
                                                loadOptions={(inputValue: string, callback: (options: SelectOption[]) => void) => {
                                                    const filteredOptions = appsDestination.filter((option) =>
                                                        option.label.toLowerCase().includes(inputValue.toLowerCase())
                                                    );
                                                    callback(filteredOptions);
                                                }}
                                                isDisabled={!serverDestination?._id}
                                                onChange={(option: SelectOption | null) => {
                                                    field.onChange(option?.value);
                                                    const name = option?.label.split(' - ID:')[0].replace('Nome: ', '') || '';
                                                    setValue("appReplaceName", name);
                                                    //console.log("🔹 Aplicativo para Replace selecionado:", name);
                                                }}
                                                placeholder="Selecione um aplicativo"
                                                /** 🔹 Mensagem enquanto carrega */
                                                isLoading={isLoadingAppsDestino}
                                                loadingMessage={() => "Carregando aplicativos..."}
                                                noOptionsMessage={() => "Nenhum aplicativo encontrado"}
                                            />
                                        )}
                                    />
                                </div>

                                {/* 🔹 Task para Execução */}
                                <div>
                                    <Label>Task para Execução</Label>
                                    <Controller
                                        defaultValue={selectedSchedule?.taskId}
                                        name="taskId"
                                        control={control}
                                        render={({ field }) => (
                                            <AsyncSelect<SelectOption>
                                                value={tasks.find(option => option.value === field.value) || null}
                                                instanceId="task-select"
                                                cacheOptions
                                                defaultOptions={tasks}
                                                loadOptions={(inputValue: string, callback: (options: SelectOption[]) => void) => {
                                                    const filteredOptions = tasks.filter((option) =>
                                                        option.label.toLowerCase().includes(inputValue.toLowerCase())
                                                    );
                                                    callback(filteredOptions);
                                                }}
                                                isDisabled={!serverDestination?._id}
                                                onChange={(option: SelectOption | null) => {
                                                    field.onChange(option?.value);
                                                    const name = option?.label.split(' - ID:')[0].replace('Nome: ', '') || '';
                                                    setValue("taskName", name);
                                                }}
                                                placeholder="Selecione uma Task"
                                            />
                                        )}
                                    />
                                </div>

                                {/* 🔹 Servidor NPrinting */}
                                <div>
                                    <Label>Servidor NPrinting</Label>
                                    <Controller
                                        name="nprintingServerId"
                                        defaultValue={selectedSchedule?.nprintingServerId}
                                        control={control}
                                        render={({ field }) => (
                                            <Select
                                                //defaultValue={field.value}
                                                //value={field.value || ""}
                                                value={field.value?._id || ""}
                                                onValueChange={(value) => {
                                                    field.onChange(value);
                                                    setNPrintingTasks([]);
                                                    setValue("nprintingTaskId", "");
                                                    setValue("nprintingTaskName", "");
                                                }}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Selecione um servidor NPrinting" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {nprintingServers.map((server) => (
                                                        <SelectItem key={server._id} value={server._id}>
                                                            {server.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        )}
                                    />
                                </div>

                                {/* 🔹 Task NPrinting */}
                                <div>
                                    <Label>Task NPrinting</Label>
                                    <Controller
                                        name="nprintingTaskId"
                                        defaultValue={selectedSchedule?.nprintingTaskId}
                                        control={control}
                                        render={({ field }) => (
                                            <AsyncSelect<SelectOption>
                                                value={nprintingTasks.find(option => option.value === field.value) || null}
                                                instanceId="nprinting-task-select"
                                                cacheOptions
                                                defaultOptions={nprintingTasks}
                                                loadOptions={(inputValue: string, callback: (options: SelectOption[]) => void) => {
                                                    const filteredOptions = nprintingTasks.filter((option) =>
                                                        option.label.toLowerCase().includes(inputValue.toLowerCase())
                                                    );
                                                    callback(filteredOptions);
                                                }}
                                                isDisabled={!nprintingServerId?._id}
                                                onChange={(option: SelectOption | null) => {
                                                    field.onChange(option?.value);
                                                    const name = option?.label.split(' - ID:')[0].replace('Nome: ', '') || '';
                                                    setValue("nprintingTaskName", name);
                                                }}
                                                placeholder="Selecione uma Task NPrinting"
                                            />
                                        )}
                                    />
                                </div>

                                {/* 🔹 Remetente SMTP */}
                                <div>
                                    <Label>Remetente</Label>
                                    <Controller
                                        defaultValue={selectedSchedule?.smtpConfigId}
                                        name="smtpConfigId"
                                        control={control}
                                        render={({ field }) => (
                                            <Select
                                                value={field.value || ""}
                                                onValueChange={field.onChange}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Selecione um remetente" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {smtpConfigs.map((config) => (
                                                        <SelectItem key={config._id} value={config._id}>
                                                            Empresa: {config.company.name} E-mail: {config.from.email}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        )}
                                    />
                                </div>

                                {/* 🔹 Destinatários */}
                                <div>
                                    <Label>Destinatários (Empresa de Origem)</Label>
                                    <Controller
                                        name="destinatarios"
                                        control={control}
                                        defaultValue={[]}
                                        render={({ field }) => (
                                            <AsyncSelect<SelectOption, true>
                                                instanceId={`destinatarios-select-${selectedSchedule?._id}`}
                                                isMulti
                                                cacheOptions
                                                value={field.value || []} // Agora usa diretamente o valor controlado pelo form
                                                defaultOptions={destinatarios}
                                                loadOptions={(inputValue: string, callback: (options: SelectOption[]) => void) => {
                                                    const filteredOptions = destinatarios.filter((option) =>
                                                        option.label.toLowerCase().includes(inputValue.toLowerCase())
                                                    );
                                                    callback(filteredOptions);
                                                }}
                                                isDisabled={!serverOrigin?._id}
                                                onChange={(selectedOptions) => {
                                                    field.onChange(selectedOptions || []);
                                                }}
                                                placeholder="Pesquise e selecione os destinatários"
                                            />
                                        )}
                                    />
                                </div>

                                {/* 🔹 Tipo de Agendamento */}
                                <div>
                                    <Label>Tipo de Agendamento</Label>
                                    <Controller
                                        name="scheduleType"
                                        control={control}
                                        defaultValue={selectedSchedule?.scheduleType}
                                        render={({ field }) => (
                                            <Select
                                                value={field.value || ""}
                                                onValueChange={field.onChange}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Selecione um tipo" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="diario">Diário</SelectItem>
                                                    <SelectItem value="semanal">Semanal</SelectItem>
                                                    <SelectItem value="mensal">Mensal</SelectItem>
                                                    <SelectItem value="personalizado">Personalizado</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        )}
                                    />
                                </div>

                                {/* 🔹 Horário de Execução */}
                                {scheduleType !== "personalizado" && (
                                    <div>
                                        <Label>Horário de Execução</Label>
                                        <Controller
                                            name="scheduleTime"
                                            control={control}
                                            defaultValue=""
                                            render={({ field }) => (
                                                <Input type="time" {...field} value={field.value || ""} />
                                            )}
                                        />
                                    </div>
                                )}

                                {/* 🔹 Datas e Horários Personalizados */}
                                {scheduleType === "personalizado" && (
                                    <div className="space-y-4">
                                        <Label>Dias da Semana e Horário</Label>

                                        <div className="flex flex-col space-y-4">
                                            <div className="flex flex-col space-y-2">
                                                <Label>Selecione os dias da semana</Label>
                                                <div className="flex gap-2 flex-wrap">
                                                    {weekDays.map((weekDay, index) => (
                                                        <Button
                                                            key={weekDay.value}
                                                            type="button"
                                                            variant={selectedWeekDays.includes(weekDay.value) ? "default" : "outline"}
                                                            className="w-10 h-10 p-0"
                                                            title={weekDay.label}
                                                            onClick={() => toggleWeekDay(weekDay.value)}
                                                        >
                                                            {weekDay.day}
                                                        </Button>
                                                    ))}
                                                </div>
                                            </div>

                                            <div className="flex flex-col space-y-2">
                                                <Label>Horário de execução</Label>
                                                <Input
                                                    type="time"
                                                    value={customTime || ""}
                                                    onChange={(e) => setCustomTime(e.target.value)}
                                                />
                                            </div>

                                            <Button
                                                type="button"
                                                variant="outline"
                                                onClick={addCustomSchedules}
                                                className="w-full"
                                            >
                                                <PlusCircle size={18} className="mr-2" />
                                                Adicionar Agendamento
                                            </Button>
                                        </div>

                                        {customSchedules.length > 0 && (
                                            <div className="mt-4">
                                                <Label>Agendamentos</Label>
                                                <div className="space-y-2 mt-2">
                                                    {customSchedules.map((schedule, index) => (
                                                        <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                                                            <span>
                                                                {formatWeekDays(schedule.weekDays)} às {schedule.time}
                                                            </span>
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                type="button"
                                                                onClick={() => removeCustomSchedule(index)}
                                                            >
                                                                <Trash2 size={18} className="text-red-500" />
                                                            </Button>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                )}

                                {/* 🔹 Switch para ativar/desativar o agendamento */}
                                <div className="flex items-center space-x-2">
                                    <Switch
                                        checked={watch("isActive")}
                                        onCheckedChange={(checked) => setValue("isActive", checked)}
                                    />
                                    <Label>Ativo</Label>
                                </div>

                                <Button type="submit" disabled={loading} className="w-full">
                                    {loading ? "Salvando..." : "Salvar Alterações"}
                                </Button>
                            </form>
                        )}
                </DialogContent>
            </Dialog>

            <Dialog open={!!scheduleToExecute} onOpenChange={() => setScheduleToExecute(null)}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Confirmar Execução</DialogTitle>
                        <DialogDescription>
                            Tem certeza que deseja executar este agendamento agora?
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setScheduleToExecute(null)}>Cancelar</Button>
                        <Button onClick={handleConfirmExecution}>Confirmar</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
} 